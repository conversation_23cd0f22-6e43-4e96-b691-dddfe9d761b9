"""
Generate Test Case Module

This module generates test cases from CSV files using GenTestCaseBussinessFlow.
Depends on: document_processor, business_flow_detector, relevant_content_processor, path_processor, path_to_csv
Uses multithreading for parallel processing.
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List
from app.core.pipeline_registry import PipelineModule, PipelineModuleConfig, PipelineModuleType

logger = logging.getLogger(__name__)

class GenTestCaseModule(PipelineModule):
    """Module for generating test cases from CSV files"""

    def __init__(self, config: Dict[str, Any] = None):
        if config is None:
            config = {}

        module_config = PipelineModuleConfig(
            id="gen_test_case",
            name="Generate Test Case",
            description="Generates test cases from CSV files using AI",
            version="1.0.0",
            module_type=PipelineModuleType.PROCESSOR,
            dependencies=[
                "document_processor",
                "business_flow_detector",
                "relevant_content_processor",
                "path_processor",
                "test_case_csv_generator"
            ]
        )
        super().__init__(module_config)

        # Configuration
        self.config_file = Path("back_end/document/gemini_config_pro.json")
        self.output_base_dir = Path(config.get("output_directory", "output"))



    async def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input data"""
        try:
            required_fields = ["document_id", "file_path"]
            for field in required_fields:
                if field not in input_data:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Check if file exists
            file_path = Path(input_data["file_path"])
            if not file_path.exists():
                logger.error(f"Input file does not exist: {file_path}")
                return False
                
            return True
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            return False

    async def execute(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute test case generation"""
        try:
            logger.info("GenTestCaseModule.execute() called")
            logger.info(f"Input data: {input_data}")
            logger.info(f"Context: {context}")

            # Extract input parameters
            document_id = input_data["document_id"]
            file_path = Path(input_data["file_path"])

            logger.info(f"Processing document: {document_id}")
            logger.info(f"Original file: {file_path}")
            
            # Create output directory for this module
            doc_output_dir = self.output_base_dir / document_id / "gen_test_case"
            doc_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Input files from previous modules
            test_case_csv_dir = self.output_base_dir / document_id / "test_case_csv_generator"

            logger.info(f"Test case CSV directory: {test_case_csv_dir}")
            logger.info(f"Output directory: {doc_output_dir}")

            # Check if test case CSV directory exists
            if not test_case_csv_dir.exists():
                raise FileNotFoundError(f"Test case CSV directory not found: {test_case_csv_dir}")
            
            # Import and execute GenTestCaseBussinessFlow
            try:
                logger.info("Importing GenTestCaseBussinessFlow...")
                
                # Import the processor class
                import sys
                sys.path.append(str(Path("back_end").absolute()))
                
                from gen_test_case import GenTestCaseBussinessFlow
                logger.info("GenTestCaseBussinessFlow imported successfully")

                logger.info("Starting test case generation in thread pool...")
                
                # Use process_from_base_dir for automatic processing with multithreading
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    GenTestCaseBussinessFlow.process_from_base_dir,
                    str(test_case_csv_dir),   # base_dir
                    str(doc_output_dir),      # output_dir
                    str(self.config_file),    # config_file
                    True                      # count_token
                )
                logger.info("Test case generation completed")

            except ImportError as e:
                logger.error(f"Failed to import GenTestCaseBussinessFlow: {e}")
                return {
                    "success": False,
                    "error": f"GenTestCaseBussinessFlow not available: {e}",
                    "document_id": document_id
                }

            # Check outputs and prepare result
            outputs = {}
            summary = {
                "test_case_files_generated": 0,
                "business_flows_processed": 0,
                "csv_files_processed": 0,
                "output_files": []
            }
            
            # Scan for JSON test case output files
            json_count = 0
            business_flow_count = 0
            csv_processed_count = 0
            
            for item in doc_output_dir.rglob("*.json"):
                json_count += 1
                outputs[f"test_case_file_{json_count}"] = str(item)
                summary["output_files"].append(str(item))
            
            # Count business flow directories processed
            for item in doc_output_dir.iterdir():
                if item.is_dir() and item.name.startswith("business_flow_"):
                    business_flow_count += 1
                    
                    # Count CSV files that were processed in this business flow
                    bf_csv_files = list(item.rglob("*.csv"))
                    csv_processed_count += len(bf_csv_files)
            
            # Update summary
            summary["test_case_files_generated"] = json_count
            summary["business_flows_processed"] = business_flow_count
            summary["csv_files_processed"] = csv_processed_count
            
            # Determine success
            success = json_count > 0
            
            if success:
                message = f"Successfully generated {summary['test_case_files_generated']} test case files from {summary['csv_files_processed']} CSV files across {summary['business_flows_processed']} business flows"
                logger.info(f"Test case generation completed: {document_id}")
            else:
                message = "No test case files generated"
                logger.warning(f"No test case files generated for: {document_id}")

            result = {
                "success": success,
                "document_id": document_id,
                "input_directory": str(path_to_csv_dir),
                "output_directory": str(doc_output_dir),
                "outputs": outputs,
                "summary": summary,
                "message": message
            }

            logger.info(f"GenTestCaseModule result: {result}")
            return result

        except Exception as e:
            error_msg = f"Test case generation failed: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "document_id": input_data.get("document_id", "unknown"),
                "outputs": {},
                "summary": {
                    "test_case_files_generated": 0,
                    "business_flows_processed": 0,
                    "csv_files_processed": 0,
                    "output_files": []
                }
            }


