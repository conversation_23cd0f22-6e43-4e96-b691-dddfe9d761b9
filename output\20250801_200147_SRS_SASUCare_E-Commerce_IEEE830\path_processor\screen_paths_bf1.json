{"bfs_paths": [{"use_case_id": "UC-301", "screen": "Edit Profile Page", "path": [{"step_number": 1, "source_screen": "Edit Profile Page", "target_screen": "Edit Profile Page", "action": "Start", "element": "Initial screen"}]}, {"use_case_id": "UC-302", "screen": "Edit Product Page", "path": [{"step_number": 1, "source_screen": "Edit Profile Page", "target_screen": "User Profile Page", "action": "saves profile changes", "element": "Save Changes <PERSON>"}, {"step_number": 2, "source_screen": "User Profile Page", "target_screen": "Home Page", "action": "clicks logout", "element": "User Account <PERSON>u"}, {"step_number": 3, "source_screen": "Home Page", "target_screen": "<PERSON><PERSON>", "action": "clicks 'Login'", "element": "User Account <PERSON>u"}, {"step_number": 4, "source_screen": "<PERSON><PERSON>", "target_screen": "Seller Dashboard", "action": "logs in as Seller", "element": "<PERSON><PERSON>"}, {"step_number": 5, "source_screen": "Seller Dashboard", "target_screen": "Seller Products Page", "action": "navigates to manage products", "element": "Navigation Menu"}, {"step_number": 6, "source_screen": "Seller Products Page", "target_screen": "Edit Product Page", "action": "clicks 'Add Product'", "element": "Add Product Button"}]}, {"use_case_id": "UC-102", "screen": "<PERSON><PERSON>", "path": [{"step_number": 1, "source_screen": "Edit Profile Page", "target_screen": "User Profile Page", "action": "saves profile changes", "element": "Save Changes <PERSON>"}, {"step_number": 2, "source_screen": "User Profile Page", "target_screen": "Home Page", "action": "clicks logout", "element": "User Account <PERSON>u"}, {"step_number": 3, "source_screen": "Home Page", "target_screen": "<PERSON><PERSON>", "action": "clicks 'Login'", "element": "User Account <PERSON>u"}]}, {"use_case_id": "UC-203", "screen": "Product Detail Page", "path": [{"step_number": 1, "source_screen": "<PERSON><PERSON>", "target_screen": "Home Page", "action": "logs in as Customer", "element": "<PERSON><PERSON>"}, {"step_number": 2, "source_screen": "Home Page", "target_screen": "Product Detail Page", "action": "clicks a product", "element": "Product Cards"}]}, {"use_case_id": "UC-207", "screen": "Checkout Page", "path": [{"step_number": 1, "source_screen": "Product Detail Page", "target_screen": "Shopping Cart Page", "action": "clicks cart icon after adding item", "element": "Shopping Cart Icon"}, {"step_number": 2, "source_screen": "Shopping Cart Page", "target_screen": "Checkout Page", "action": "clicks 'Checkout'", "element": "Checkout <PERSON><PERSON>"}]}, {"use_case_id": "UC-212", "screen": "Customer Bookings Page", "path": [{"step_number": 1, "source_screen": "Customer Bookings Page", "target_screen": "Customer Bookings Page", "action": "Start", "element": "Initial screen"}]}]}