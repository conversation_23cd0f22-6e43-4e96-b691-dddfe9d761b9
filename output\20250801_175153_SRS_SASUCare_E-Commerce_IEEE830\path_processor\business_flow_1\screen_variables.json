{"screen_definitions": [{"screen_name": "Home Page", "variables": {}}, {"screen_name": "Product Detail Page", "variables": {"options": {"valid": [], "invalid": []}, "quantity": {"valid": ["1", "99", "integer_within_stock"], "invalid": ["0", "-1", "100", "non-integer", ">available_stock"]}, "button_action": {"valid": ["Add to Cart"], "invalid": []}}}, {"screen_name": "Customer Registration Page", "variables": {"email": {"valid": ["<EMAIL>"], "invalid": ["<EMAIL>", "invalid-email-format", "<EMAIL>"]}, "password": {"valid": ["ValidPass1!"], "invalid": ["short", "nouppercase1!", "NOLOWERCASE1!", "NoNumber!", "NoSpecialChar1", "contains_email"]}, "confirm password": {"valid": ["matches_password"], "invalid": ["does_not_match_password"]}, "first name": {"valid": [], "invalid": []}, "last name": {"valid": [], "invalid": []}, "Terms and Conditions checkbox": {"valid": ["checked"], "invalid": ["unchecked"]}, "Privacy Policy acknowledgment": {"valid": ["checked"], "invalid": ["unchecked"]}, "Marketing consent": {"valid": ["checked", "unchecked"], "invalid": []}, "button_action": {"valid": ["Register"], "invalid": []}}}, {"screen_name": "Shopping Cart Page", "variables": {"Street address": {"valid": ["5-100 alphanumeric chars"], "invalid": ["shrt", "string_longer_than_100_characters_for_testing_street_address_validation_rules_to_ensure_it_is_working_correctly", "invalid_chars_!@#"]}, "City": {"valid": ["2-50 letters and spaces"], "invalid": ["A", "CityNameThatIsMuchLongerThanFiftyCharactersAndShouldFail", "City123"]}, "ZIP/Postal code": {"valid": ["12345", "12345-6789"], "invalid": ["123", "abcde"]}, "Phone number": {"valid": ["1234567890"], "invalid": ["12345", "1234567890123456", "abc"]}, "payment method": {"valid": [], "invalid": []}, "Credit card number": {"valid": ["13-19_luhn_valid_digits"], "invalid": ["12_digits", "20_digits", "luhn_invalid", "non-digits"]}, "CVV": {"valid": ["123", "1234"], "invalid": ["12", "12345", "abc"]}, "Expiry date": {"valid": ["MM/YY_future_date"], "invalid": ["MM/YY_past_date", "99/99", "invalid_format"]}, "button_action": {"valid": ["Confirm Order"], "invalid": []}}}, {"screen_name": "Customer Bookings Page", "variables": {}}, {"screen_name": "Booking Details Page", "variables": {"button_action": {"valid": ["Confirm Cancellation"], "invalid": ["cancellation_deadline_passed"]}}}, {"screen_name": "Create Booking Page", "variables": {"Service selection": {"valid": ["available_service"], "invalid": ["unavailable_service"]}, "Date and Time": {"valid": ["available_datetime"], "invalid": ["unavailable_datetime", "past_datetime"]}, "button_action": {"valid": ["Submit Booking"], "invalid": []}}}, {"screen_name": "User Profile Page", "variables": {}}, {"screen_name": "Edit Profile Page", "variables": {"shop name": {"valid": ["3-50_alphanumeric_unique_name"], "invalid": ["s1", "string_longer_than_50_characters_for_testing_shop_name", "duplicate_shop_name", "profane_word_in_name"]}, "Business name": {"valid": ["2-100_chars"], "invalid": ["a", "a_name_that_is_very_long_and_definitely_over_one_hundred_characters_to_properly_test_the_validation_logic"]}, "Business description": {"valid": ["10-500_chars"], "invalid": ["too_short", "a_description_that_is_very_long_and_definitely_over_five_hundred_characters_to_properly_test_the_validation_logic_this_will_go_on_for_a_while_to_make_sure_it_crosses_the_threshold_and_then_some_more_just_to_be_safe_and_ensure_that_the_system_correctly_identifies_it_as_an_invalid_input_based_on_the_length_constraint_that_has_been_defined_in_the_business_rules_for_the_seller_profile_management_use_case_as_specified_in_the_requirements_document_provided_as_input_for_this_task_and_continues_on_and_on_and_on_and_on."]}, "Contact phone": {"valid": ["valid_phone_format"], "invalid": ["invalid_phone_format"]}, "Business address": {"valid": ["complete_address"], "invalid": ["incomplete_address"]}, "shop logo/banner images": {"valid": ["JPEG_or_PNG_or_WebP_file", "file_under_5MB", "image_within_200x200_to_2048x2048"], "invalid": ["gif_file", "file_over_5MB", "image_under_200x200", "image_over_2048x2048"]}, "button_action": {"valid": ["save changes"], "invalid": []}}}, {"screen_name": "User Orders Page", "variables": {}}, {"screen_name": "Seller Dashboard", "variables": {}}, {"screen_name": "Seller Products Page", "variables": {"button_action": {"valid": ["Delete product"], "invalid": []}}}, {"screen_name": "Edit Product Page", "variables": {"Product name": {"valid": ["valid-product-name"], "invalid": ["four", "a_product_name_that_is_very_long_and_definitely_over_one_hundred_characters_to_properly_test_validation", "invalid_char_@"]}, "Description": {"valid": ["valid_description_over_20_chars"], "invalid": ["too_short_desc", "description_longer_than_2000_chars"]}, "Price": {"valid": ["50.00"], "invalid": ["0.00", "100000.00", "50.123"]}, "Sale price": {"valid": ["40.00"], "invalid": ["60.00"]}, "SKU": {"valid": ["UNIQUE-SKU-123"], "invalid": ["s1", "a_very_long_sku_that_is_over_the_fifty_character_limit", "duplicate_sku"]}, "Stock quantity": {"valid": ["100"], "invalid": ["-1", "10000", "abc"]}, "images": {"valid": ["<=10_images"], "invalid": [">10_images"]}, "button_action": {"valid": ["Save", "Update"], "invalid": []}}}, {"screen_name": "Seller Orders Page", "variables": {}}, {"screen_name": "Seller Order Details Page", "variables": {"order status": {"valid": ["Processing", "Shipped", "Delivered"], "invalid": ["invalid_transition_from_Pending_to_Shipped"]}, "Tracking number": {"valid": ["8-50_alphanumeric_chars"], "invalid": ["short", "a_very_long_tracking_number_that_is_over_the_fifty_character_limit_for_testing"]}, "Carrier": {"valid": ["UPS", "FedEx", "USPS", "DHL"], "invalid": ["unlisted_carrier"]}, "button_action": {"valid": ["save changes"], "invalid": []}}}, {"screen_name": "Se<PERSON> Bookings Page", "variables": {}}, {"screen_name": "Seller Booking Details Page", "variables": {"booking status": {"valid": ["Cancelled"], "invalid": []}, "button_action": {"valid": ["save changes"], "invalid": []}}}, {"screen_name": "Admin Dashboard", "variables": {}}, {"screen_name": "Admin Products Page", "variables": {"Search/filter": {"valid": ["valid_search_term"], "invalid": []}, "button_action": {"valid": ["Edit", "Delete"], "invalid": ["delete_product_with_active_orders"]}}}, {"screen_name": "Admin Pending Products Page", "variables": {"button_action": {"valid": ["Approve", "Reject"], "invalid": []}}}], "navigation_rules": {"(Home Page, Login Page)": "User Account <PERSON>u", "(Home Page, Customer Registration Page)": "Main Navigation", "(Home Page, Search Results Page)": "Search Bar", "(Home Page, Product Detail Page)": "Product Cards", "(Home Page, Shopping Cart Page)": "Shopping Cart Icon", "(Home Page, About Us Page)": "Main Navigation", "(Home Page, Contact Page)": "Main Navigation", "(Home Page, Collaborations Page)": "Main Navigation", "(Login Page, Home Page)": "<PERSON><PERSON>", "(Login Page, Seller Dashboard)": "<PERSON><PERSON>", "(Login Page, Admin Dashboard)": "<PERSON><PERSON>", "(Login Page, Customer Registration Page)": "Registration Links", "(Login Page, Seller Registration Page)": "Registration Links", "(Customer Registration Page, Login Page)": "Register <PERSON>", "(Seller Registration Page, Login Page)": "Register <PERSON>", "(Search Results Page, Product Detail Page)": "Results Grid", "(Shopping Cart Page, Home Page)": "Continue Shopping Button", "(Shopping Cart Page, User Orders Page)": "Checkout <PERSON><PERSON>", "(Home Page, User Profile Page)": "User Account <PERSON>u", "(User Profile Page, User Orders Page)": "Account <PERSON><PERSON>", "(User Profile Page, Customer Bookings Page)": "Account <PERSON><PERSON>", "(User Profile Page, Edit Profile Page)": "Account <PERSON><PERSON>", "(User Profile Page, Change Password Page)": "Account <PERSON><PERSON>", "(Edit Profile Page, User Profile Page)": "Save Changes <PERSON> or Cancel Button", "(Change Password Page, User Profile Page)": "Change Password <PERSON>", "(Customer Bookings Page, Booking Details Page)": "Booking Details Link", "(Customer Bookings Page, Create Booking Page)": "Create New Booking", "(Create Booking Page, Customer Bookings Page)": "Submit Booking Button", "(Booking Details Page, Customer Bookings Page)": "Action Buttons or Back to Bookings", "(Seller Dashboard, Seller Products Page)": "Navigation Menu", "(Seller Dashboard, Seller Orders Page)": "Navigation Menu", "(Seller Dashboard, Seller Bookings Page)": "Navigation Menu", "(Seller Products Page, Edit Product Page)": "Product Actions or Add Product Button", "(Edit Product Page, Seller Products Page)": "Save/Update But<PERSON>", "(Seller Orders Page, Seller Order Details Page)": "Order Details Link", "(Seller Order Details Page, Seller Orders Page)": "Back to Orders", "(Seller Bookings Page, Seller Booking Details Page)": "Booking Details Link", "(Seller Booking Details Page, Seller Bookings Page)": "Back to Bookings", "(Admin Dashboard, Admin Products Page)": "Navigation Menu", "(Admin Dashboard, Admin Pending Products Page)": "Navigation Menu", "(Admin Dashboard, Admin Categories Page)": "Navigation Menu", "(Admin Dashboard, Admin Users Page)": "Navigation Menu", "(Admin Dashboard, Admin Orders Page)": "Navigation Menu", "(Admin Dashboard, Admin Sale Codes Page)": "Navigation Menu", "(Admin Orders Page, Admin Order Details Page)": "Order Details Link", "(Admin Order Details Page, Admin Orders Page)": "Admin Actions", "(Error Page, Home Page)": "Home Page Link", "(Access Denied Page, Home Page)": "Navigation Options", "(Access Denied Page, Login Page)": "Login Suggestion"}}