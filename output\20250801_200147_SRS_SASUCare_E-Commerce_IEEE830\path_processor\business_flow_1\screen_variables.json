{"screen_definitions": [{"screen_name": "Home Page", "variables": {}}, {"screen_name": "Product Detail Page", "variables": {"options (size, color, etc.)": {"valid": ["selected_option"], "invalid": ["unselected_option"]}, "quantity": {"valid": ["1", "99"], "invalid": ["0", "100", "exceeds_available_stock"]}, "button_action": {"valid": ["Add to Cart"], "invalid": []}}}, {"screen_name": "Search Results Page", "variables": {}}, {"screen_name": "About Us Page", "variables": {}}, {"screen_name": "Contact Page", "variables": {}}, {"screen_name": "Collaborations Page", "variables": {}}, {"screen_name": "<PERSON><PERSON>", "variables": {"email": {"valid": ["<EMAIL>"], "invalid": ["<EMAIL>", "<EMAIL>"]}, "password": {"valid": ["correct_password"], "invalid": ["incorrect_password"]}, "Remember me": {"valid": ["checked", "unchecked"], "invalid": []}, "CAPTCHA": {"valid": ["correct_captcha_input"], "invalid": ["incorrect_captcha_input"]}, "button_action": {"valid": ["<PERSON><PERSON>"], "invalid": []}}}, {"screen_name": "Customer Registration Page", "variables": {"email": {"valid": ["<EMAIL>"], "invalid": ["<EMAIL>", "invalid_email_format", "domain_with_no_mx_record"]}, "password": {"valid": ["ValidP@ssword1"], "invalid": ["short", "toolongpasswordthatgoesoveronetwentyeightcharacterslimitwhichisnotallowedasperbusinessrules", "nouppercase1@", "NOLOWERCASE1@", "NoSpecialChar1", "nonumber@PASS"]}, "confirms password": {"valid": ["matches_password"], "invalid": ["does_not_match_password"]}, "first name": {"valid": ["<PERSON>"], "invalid": []}, "last name": {"valid": ["<PERSON><PERSON>"], "invalid": []}, "Terms and Conditions checkbox": {"valid": ["checked"], "invalid": ["unchecked"]}, "Privacy Policy acknowledgment": {"valid": ["checked"], "invalid": ["unchecked"]}, "Marketing consent": {"valid": ["checked", "unchecked"], "invalid": []}, "button_action": {"valid": ["Register"], "invalid": []}}}, {"screen_name": "Seller Registration Page", "variables": {"email": {"valid": ["<EMAIL>"], "invalid": ["<EMAIL>", "invalid_email_format", "domain_with_no_mx_record"]}, "password": {"valid": ["ValidP@ssword1"], "invalid": ["short", "toolongpasswordthatgoesoveronetwentyeightcharacterslimitwhichisnotallowedasperbusinessrules", "nouppercase1@", "NOLOWERCASE1@", "NoSpecialChar1", "nonumber@PASS"]}, "confirms password": {"valid": ["matches_password"], "invalid": ["does_not_match_password"]}, "first name": {"valid": ["<PERSON>"], "invalid": []}, "last name": {"valid": ["<PERSON><PERSON>"], "invalid": []}, "shop name": {"valid": ["A Valid Shop Name"], "invalid": ["S", "a_very_long_shop_name_that_is_definitely_over_the_fifty_character_limit", "profane_shop_name", "duplicate_shop_name"]}, "Terms and Conditions checkbox": {"valid": ["checked"], "invalid": ["unchecked"]}, "Privacy Policy acknowledgment": {"valid": ["checked"], "invalid": ["unchecked"]}, "Marketing consent": {"valid": ["checked", "unchecked"], "invalid": []}, "button_action": {"valid": ["Register"], "invalid": []}}}, {"screen_name": "Shopping Cart Page", "variables": {"item_quantity": {"valid": ["50"], "invalid": ["0", "100"]}}}, {"screen_name": "Customer Bookings Page", "variables": {"button_action": {"valid": ["Cancel Booking", "Confirm Cancellation"], "invalid": []}}}, {"screen_name": "Booking Details Page", "variables": {}}, {"screen_name": "Create Booking Page", "variables": {"service_selection": {"valid": [], "invalid": []}, "address_information": {"valid": [], "invalid": []}, "date": {"valid": [], "invalid": []}, "time": {"valid": [], "invalid": []}, "special_instructions": {"valid": [], "invalid": []}, "button_action": {"valid": ["Submit Booking"], "invalid": []}}}, {"screen_name": "User Profile Page", "variables": {}}, {"screen_name": "Edit Profile Page", "variables": {"shop name": {"valid": ["valid_length_and_chars"], "invalid": ["sh", "this shop name is far too long to be considered valid", "invalid_chars_!@", "duplicate_name"]}, "shop logo/banner images": {"valid": ["valid_format_size_dimensions.jpg"], "invalid": ["invalid_format.gif", "too_large_6mb.png", "too_small_dims_100x100.jpg"]}, "Business name": {"valid": ["Valid Business Name"], "invalid": ["a", "a very long business name that exceeds the one hundred character limit specified in the business rules for validation"]}, "Business description": {"valid": ["This is a valid business description with enough characters."], "invalid": ["short", "a very long business description that exceeds the five hundred character limit specified in the business rules for validation, this text will continue on and on to make sure it is long enough to fail the validation check."]}, "Contact phone": {"valid": ["************"], "invalid": ["invalid_phone_format"]}, "Business address": {"valid": ["complete_address"], "invalid": ["incomplete_address"]}, "button_action": {"valid": ["Save"], "invalid": []}}}, {"screen_name": "Change Password Page", "variables": {"current_password": {"valid": [], "invalid": []}, "new_password": {"valid": [], "invalid": []}, "confirm_new_password": {"valid": [], "invalid": []}, "button_action": {"valid": ["Update"], "invalid": []}}}, {"screen_name": "User Orders Page", "variables": {}}, {"screen_name": "Seller Dashboard", "variables": {}}, {"screen_name": "Seller Products Page", "variables": {"button_action": {"valid": ["Add", "Edit", "Delete"], "invalid": []}}}, {"screen_name": "Edit Product Page", "variables": {"Product name": {"valid": ["valid-product-name"], "invalid": ["shrt", "a_very_long_product_name_that_is_much_longer_than_the_one_hundred_character_limit_and_should_fail_validation", "invalid_!@#_chars"]}, "Description": {"valid": ["This is a perfectly valid product description that meets the length requirement."], "invalid": ["short desc", "description_longer_than_2000_chars"]}, "Price": {"valid": ["10.99", "99999.99"], "invalid": ["0.00", "100000.00", "10.999"]}, "Sale price": {"valid": ["sale_price_lower_than_regular"], "invalid": ["sale_price_higher_than_regular"]}, "SKU": {"valid": ["VALIDSKU123"], "invalid": ["S1", "a_very_long_sku_that_is_over_the_fifty_character_limit", "invalid_!#_chars", "duplicate_sku"]}, "product images": {"valid": ["valid_image.jpg"], "invalid": ["invalid_format.gif", "too_large.png", "eleventh_image.jpg"]}, "Stock quantity": {"valid": ["500", "9999", "0"], "invalid": ["-1", "10000"]}, "button_action": {"valid": ["Save"], "invalid": []}}}, {"screen_name": "Seller Orders Page", "variables": {}}, {"screen_name": "Seller Order Details Page", "variables": {"order status": {"valid": ["Processing", "Shipped", "Delivered"], "invalid": ["Shipped_from_Pending"]}, "tracking information": {"valid": ["VALIDTRACKING12345"], "invalid": ["short", "a_very_long_tracking_number_that_is_over_the_fifty_character_limit", "empty_when_status_is_shipped"]}, "Carrier selection": {"valid": ["UPS", "FedEx", "USPS", "DHL"], "invalid": ["OtherCarrier"]}, "button_action": {"valid": ["Update"], "invalid": []}}}, {"screen_name": "Se<PERSON> Bookings Page", "variables": {}}, {"screen_name": "Seller Booking Details Page", "variables": {"booking_status": {"valid": [], "invalid": []}, "button_action": {"valid": ["Update Status"], "invalid": []}}}, {"screen_name": "Admin Dashboard", "variables": {}}, {"screen_name": "Admin Products Page", "variables": {"search/filters": {"valid": [], "invalid": []}, "button_action": {"valid": ["Approve", "Reject", "Edit", "Remove"], "invalid": []}}}, {"screen_name": "Admin Pending Products Page", "variables": {"button_action": {"valid": ["Approve", "Reject"], "invalid": []}}}, {"screen_name": "Admin Categories Page", "variables": {"button_action": {"valid": ["Create", "Organize"], "invalid": []}}}, {"screen_name": "Admin Users Page", "variables": {"button_action": {"valid": ["Suspend", "Delete", "Edit Role"], "invalid": []}}}, {"screen_name": "Admin Orders Page", "variables": {}}, {"screen_name": "Admin Order Details Page", "variables": {}}, {"screen_name": "Admin Sale Codes Page", "variables": {"promo_code_name": {"valid": [], "invalid": []}, "discount_value": {"valid": [], "invalid": []}, "expiry_date": {"valid": [], "invalid": []}, "button_action": {"valid": ["Create", "Edit", "Delete"], "invalid": []}}}, {"screen_name": "Access Denied Page", "variables": {}}, {"screen_name": "Error <PERSON>", "variables": {}}, {"screen_name": "Checkout Page", "variables": {"Street address": {"valid": ["123 Main St"], "invalid": ["123", "a very long street address that exceeds the one hundred character limit specified in the business rules for this field"]}, "City": {"valid": ["New York"], "invalid": ["a", "a very long city name that is over the fifty character limit for validation", "City123"]}, "ZIP/Postal code": {"valid": ["12345", "12345-6789"], "invalid": ["invalid_zip"]}, "Phone number": {"valid": ["+***********"], "invalid": ["123456789", "1234567890123456", "invalid_chars_phone"]}, "Credit card number": {"valid": ["luhn_valid_13_to_19_digits"], "invalid": ["luhn_invalid", "123456789012", "12345678901234567890"]}, "CVV": {"valid": ["123", "1234"], "invalid": ["12", "12345"]}, "Expiry date": {"valid": ["12/99"], "invalid": ["01/20", "12-99"]}, "button_action": {"valid": ["Confirm Order"], "invalid": []}}}, {"screen_name": "Order Confirmation Page", "variables": {}}, {"screen_name": "User Order Details Page", "variables": {}}, {"screen_name": "Password Recovery Page", "variables": {"email": {"valid": ["<EMAIL>"], "invalid": ["<EMAIL>", "invalid_format"]}, "button_action": {"valid": ["Send Reset Link"], "invalid": []}}}], "navigation_rules": {"(Home Page, Login Page)": "User Account <PERSON>u", "(Home Page, Customer Registration Page)": "User Account <PERSON>u", "(Home Page, Product Detail Page)": "Product Cards", "(Home Page, Search Results Page)": "Search Bar", "(Customer Registration Page, Login Page)": "Register <PERSON>", "(Seller Registration Page, Login Page)": "Register <PERSON>", "(Login Page, Home Page)": "<PERSON><PERSON>", "(Login Page, Seller Dashboard)": "<PERSON><PERSON>", "(Login Page, Admin Dashboard)": "<PERSON><PERSON>", "(Login Page, Customer Registration Page)": "Registration Links", "(Login Page, Seller Registration Page)": "Registration Links", "(Login Page, Password Recovery Page)": "Forgot Password Link", "(Search Results Page, Product Detail Page)": "Results Grid", "(Product Detail Page, Shopping Cart Page)": "Shopping Cart Icon", "(Shopping Cart Page, Checkout Page)": "Checkout <PERSON><PERSON>", "(Shopping Cart Page, Home Page)": "Continue Shopping Button", "(Checkout Page, Order Confirmation Page)": "Confirm Order Button", "(Order Confirmation Page, User Orders Page)": "View Order History Link", "(User Profile Page, User Orders Page)": "Account <PERSON><PERSON>", "(User Profile Page, Customer Bookings Page)": "Account <PERSON><PERSON>", "(User Profile Page, Edit Profile Page)": "Account <PERSON><PERSON>", "(User Profile Page, Change Password Page)": "Account <PERSON><PERSON>", "(Edit Profile Page, User Profile Page)": "Save Changes <PERSON>", "(Change Password Page, User Profile Page)": "Change Password <PERSON>", "(User Orders Page, User Order Details Page)": "Order Details Link", "(Customer Bookings Page, Booking Details Page)": "Booking Details Link", "(Customer Bookings Page, Create Booking Page)": "Create New Booking", "(Booking Details Page, Customer Bookings Page)": "Action Buttons", "(Seller Dashboard, Seller Products Page)": "Navigation Menu", "(Seller Dashboard, Seller Orders Page)": "Navigation Menu", "(Seller Dashboard, Seller Bookings Page)": "Navigation Menu", "(Seller Products Page, Edit Product Page)": "Add Product Button or Product Actions", "(Edit Product Page, Seller Products Page)": "Save/Update But<PERSON>", "(Seller Orders Page, Seller Order Details Page)": "Order Details Link", "(Seller Bookings Page, Seller Booking Details Page)": "Booking Details Link", "(Admin Dashboard, Admin Products Page)": "Navigation Menu", "(Admin Dashboard, Admin Pending Products Page)": "Navigation Menu", "(Admin Dashboard, Admin Categories Page)": "Navigation Menu", "(Admin Dashboard, Admin Users Page)": "Navigation Menu", "(Admin Dashboard, Admin Orders Page)": "Navigation Menu", "(Admin Dashboard, Admin Sale Codes Page)": "Navigation Menu", "(Admin Orders Page, Admin Order Details Page)": "Order Details Link", "(Admin Pending Products Page, Admin Products Page)": "Approval Actions", "(Home Page, About Us Page)": "Main Navigation", "(Home Page, Contact Page)": "Main Navigation", "(Home Page, Collaborations Page)": "Main Navigation", "(User Profile Page, Home Page)": "User Account <PERSON>u", "(Seller Dashboard, Home Page)": "User Account <PERSON>u", "(Admin Dashboard, Home Page)": "User Account <PERSON>u"}}