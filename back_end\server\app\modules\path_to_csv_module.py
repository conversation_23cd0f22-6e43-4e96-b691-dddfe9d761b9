"""
Path to CSV Module

This module converts screen paths to CSV test cases using ScreenTestCaseGenerator.
Depends on: document_processor, business_flow_detector, relevant_content_processor, path_processor
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List
from app.core.pipeline_registry import PipelineModule, PipelineModuleConfig, PipelineModuleType

logger = logging.getLogger(__name__)

class PathToCsvModule(PipelineModule):
    """Module for converting screen paths to CSV test cases"""

    def __init__(self, config: Dict[str, Any] = None):
        if config is None:
            config = {}

        module_config = PipelineModuleConfig(
            id="path_to_csv",
            name="Path to CSV",
            description="Converts screen paths to CSV test cases",
            version="1.0.0",
            module_type=PipelineModuleType.PROCESSOR,
            dependencies=[
                "document_processor",
                "business_flow_detector",
                "relevant_content_processor",
                "path_processor"
            ]
        )
        super().__init__(module_config)

        # Configuration
        self.config_file = Path("back_end/gemini_config.json")
        self.output_base_dir = Path(config.get("output_directory", "output"))



    async def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input data"""
        try:
            required_fields = ["document_id", "file_path"]
            for field in required_fields:
                if field not in input_data:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Check if file exists
            file_path = Path(input_data["file_path"])
            if not file_path.exists():
                logger.error(f"Input file does not exist: {file_path}")
                return False
                
            return True
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            return False

    async def execute(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute path to CSV conversion"""
        try:
            logger.info("PathToCsvModule.execute() called")
            logger.info(f"Input data: {input_data}")
            logger.info(f"Context: {context}")

            # Extract input parameters
            document_id = input_data["document_id"]
            file_path = Path(input_data["file_path"])

            logger.info(f"Processing document: {document_id}")
            logger.info(f"Original file: {file_path}")
            
            # Create output directory for this module
            doc_output_dir = self.output_base_dir / document_id / "path_to_csv"
            doc_output_dir.mkdir(parents=True, exist_ok=True)

            # Base directory for the document (contains path_processor subdirectory)
            document_base_dir = self.output_base_dir / document_id
            path_processor_dir = document_base_dir / "path_processor"

            logger.info(f"Document base directory: {document_base_dir}")
            logger.info(f"Path processor directory: {path_processor_dir}")
            logger.info(f"Output directory: {doc_output_dir}")

            # Check if path processor directory exists
            if not path_processor_dir.exists():
                raise FileNotFoundError(f"Path processor directory not found: {path_processor_dir}")

            # Import and execute ScreenTestCaseGenerator
            try:
                logger.info("Importing ScreenTestCaseGenerator...")

                # Import the processor class
                import sys
                sys.path.append(str(Path("back_end").absolute()))

                from path_to_csv import ScreenTestCaseGenerator
                logger.info("ScreenTestCaseGenerator imported successfully")

                logger.info("Starting path to CSV conversion in thread pool...")

                # Use process_from_base_dir for automatic processing
                # Pass document_base_dir so it can find path_processor subdirectory
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    ScreenTestCaseGenerator.process_from_base_dir,
                    str(document_base_dir),   # base_dir (contains path_processor/)
                    str(doc_output_dir)       # output_dir
                )
                logger.info("Path to CSV conversion completed")

            except ImportError as e:
                logger.error(f"Failed to import ScreenTestCaseGenerator: {e}")
                return {
                    "success": False,
                    "error": f"ScreenTestCaseGenerator not available: {e}",
                    "document_id": document_id
                }

            # Check outputs and prepare result
            outputs = {}
            summary = {
                "csv_files_generated": 0,
                "business_flows_processed": 0,
                "output_files": []
            }
            
            # Scan for CSV output files
            csv_count = 0
            business_flow_count = 0
            
            for item in doc_output_dir.rglob("*.csv"):
                csv_count += 1
                outputs[f"csv_file_{csv_count}"] = str(item)
                summary["output_files"].append(str(item))
            
            # Count business flow directories processed
            for item in doc_output_dir.iterdir():
                if item.is_dir() and item.name.startswith("business_flow_"):
                    business_flow_count += 1
            
            # Update summary
            summary["csv_files_generated"] = csv_count
            summary["business_flows_processed"] = business_flow_count
            
            # Determine success
            success = csv_count > 0
            
            if success:
                message = f"Successfully generated {summary['csv_files_generated']} CSV files for {summary['business_flows_processed']} business flows"
                logger.info(f"Path to CSV conversion completed: {document_id}")
            else:
                message = "No CSV files generated"
                logger.warning(f"No CSV files generated for: {document_id}")

            result = {
                "success": success,
                "document_id": document_id,
                "input_directory": str(document_base_dir),
                "output_directory": str(doc_output_dir),
                "outputs": outputs,
                "summary": summary,
                "message": message
            }

            logger.info(f"PathToCsvModule result: {result}")
            return result

        except Exception as e:
            error_msg = f"Path to CSV conversion failed: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "document_id": input_data.get("document_id", "unknown"),
                "outputs": {},
                "summary": {
                    "csv_files_generated": 0,
                    "business_flows_processed": 0,
                    "output_files": []
                }
            }


