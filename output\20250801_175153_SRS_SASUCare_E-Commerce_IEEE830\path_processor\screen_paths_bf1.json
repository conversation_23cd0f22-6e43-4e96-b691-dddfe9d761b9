{"bfs_paths": [{"use_case_id": "UC-301", "screen": "Edit Profile Page", "path": [{"step_number": 1, "source_screen": "Edit Profile Page", "target_screen": "Edit Profile Page", "action": "Start", "element": "Initial screen"}]}, {"use_case_id": "UC-203", "screen": "Product Detail Page", "path": [{"step_number": 1, "source_screen": "Customer Registration Page", "target_screen": "<PERSON><PERSON>", "action": "submits registration", "element": "Register <PERSON>"}, {"step_number": 2, "source_screen": "<PERSON><PERSON>", "target_screen": "Home Page", "action": "logs in as Customer", "element": "<PERSON><PERSON>"}, {"step_number": 3, "source_screen": "Home Page", "target_screen": "Product Detail Page", "action": "clicks product", "element": "Product Cards"}]}, {"use_case_id": "UC-305", "screen": "Seller Orders Page", "path": [{"step_number": 1, "source_screen": "Shopping Cart Page", "target_screen": "Home Page", "action": "continues shopping", "element": "Continue Shopping Button"}, {"step_number": 2, "source_screen": "Home Page", "target_screen": "<PERSON><PERSON>", "action": "clicks 'Login'", "element": "User Account <PERSON>u"}, {"step_number": 3, "source_screen": "<PERSON><PERSON>", "target_screen": "Seller Dashboard", "action": "logs in as Seller", "element": "<PERSON><PERSON>"}, {"step_number": 4, "source_screen": "Seller Dashboard", "target_screen": "Seller Orders Page", "action": "navigates to manage orders", "element": "Navigation Menu"}]}, {"use_case_id": "UC-212", "screen": "Booking Details Page", "path": [{"step_number": 1, "source_screen": "Customer Bookings Page", "target_screen": "Booking Details Page", "action": "views booking details", "element": "Booking Details Link"}]}]}