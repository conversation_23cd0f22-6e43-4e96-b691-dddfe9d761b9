{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a3a1cb43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------------------------\n", "name='cachedContents/j8umsdq3qy0jhosvaja8uakr853j2ui9n3v1ean6' display_name='merged_output' model='models/gemini-2.5-pro' create_time=datetime.datetime(2025, 8, 1, 1, 15, 53, 212576, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 1, 15, 53, 212576, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 2, 15, 52, 514415, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=22734, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/l29dij3za4hxr2i0f59f5r0ofdlovqucre1tqive' display_name='relevant_info_BP_16' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 54, 46, 913641, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 54, 46, 913641, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 54, 46, 472790, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=61922, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/haqe5w9zvvvbeb31oeh2px4pl0mgg4ilr9dmrkao' display_name='relevant_info_BP_15' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 54, 26, 537866, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 54, 26, 537866, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 54, 26, 197953, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=6910, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/39mhxbe90b17bcmrt3yxx0xwz626j109e4wj3czt' display_name='relevant_info_BP_14' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 54, 23, 592217, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 54, 23, 592217, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 54, 23, 193710, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=43834, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/zbo3x43sryx2jdvzg1g3sgt1axpnevnmwannnq8w' display_name='relevant_info_BP_13' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 54, 15, 196554, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 54, 15, 196554, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 54, 14, 843835, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=34205, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/2aure38kib7rhqhemhwy82ia83f4z8lnpa0jjt8f' display_name='relevant_info_BP_12' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 40, 38, 243577, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 40, 38, 243577, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 40, 37, 910394, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=24512, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/x0p72hatkxq7wx750e4zhgeriv7z4jo1yshuljfp' display_name='relevant_info_BP_10' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 40, 10, 147738, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 40, 10, 147738, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 40, 9, 729233, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=36712, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/k5y1a47wljcuk30joll7an7w26rpj2oo54m4mn6r' display_name='relevant_info_BP_11' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 40, 9, 852586, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 40, 9, 852586, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 40, 9, 515241, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=34795, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/8go0jwushi1pcdcuobv4w9egfi0anuey61varlyb' display_name='relevant_info_BP_9' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 40, 4, 679165, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 40, 4, 679165, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 40, 4, 311641, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=38680, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/mkuu814h2zp9by5gkd2vlblz7kvfj5tg8tkihdf6' display_name='relevant_info_BP_8' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 39, 37, 958913, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 39, 37, 958913, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 39, 37, 497022, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=40542, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/bk2eujqdmcyqnrr489bdltqzqya7myevr5jlyka6' display_name='relevant_info_BP_7' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 39, 32, 63426, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 39, 32, 63426, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 39, 31, 601519, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=50030, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/29m0xjxi7rb0ytuyry1jyxgzpo2ufx961gls08lc' display_name='relevant_info_BP_6' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 39, 26, 446893, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 39, 26, 446893, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 39, 25, 984574, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=32882, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/bxjes3yw85sb62w1vbw9fjpq6zs3qojvkkyb4lag' display_name='relevant_info_BP_4' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 39, 4, 611181, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 39, 4, 611181, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 39, 4, 157863, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=43430, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/dv311ogikm5go75mc1js0z78yxulsnzwegqcnw5p' display_name='relevant_info_BP_5' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 38, 55, 333678, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 38, 55, 333678, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 38, 54, 726779, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=36671, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/zpsc1vtjbuqu22m0m32a3z7k3wzaalhs6mfob54n' display_name='relevant_info_BP_3' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 38, 52, 803991, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 38, 52, 803991, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 38, 52, 480836, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=41134, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/4525cwaomuym3tmw74uwixr2oo9n2u6c6sii1zwc' display_name='relevant_info_BP_2' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 38, 24, 89299, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 38, 24, 89299, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 38, 23, 603807, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=30465, video_duration_seconds=None)\n", "--------------------------------------------------------\n", "name='cachedContents/riyz4w71xr0qqqahqv78wpmc991ygr08qlr3kmd7' display_name='relevant_info_BP_1' model='models/gemini-2.5-flash' create_time=datetime.datetime(2025, 8, 1, 0, 38, 15, 334215, tzinfo=TzInfo(UTC)) update_time=datetime.datetime(2025, 8, 1, 0, 38, 15, 334215, tzinfo=TzInfo(UTC)) expire_time=datetime.datetime(2025, 8, 1, 1, 38, 14, 967703, tzinfo=TzInfo(UTC)) usage_metadata=CachedContentUsageMetadata(audio_duration_seconds=None, image_count=None, text_count=None, total_token_count=27141, video_duration_seconds=None)\n"]}], "source": ["from google import genai\n", "\n", "# Khởi tạo client với API key\n", "client = genai.Client(api_key=\"AIzaSyALkOnGLkvxoh_ds3r5RALI51814sNO7Jg\")\n", "\n", "for cache in client.caches.list():\n", "  print(\"--------------------------------------------------------\")\n", "\n", "  client.caches.delete(name=cache.name)\n", "  print(cache)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}