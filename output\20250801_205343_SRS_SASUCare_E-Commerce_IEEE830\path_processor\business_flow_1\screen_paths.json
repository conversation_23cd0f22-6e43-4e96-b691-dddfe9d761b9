{"bfs_paths": [{"use_case_id": "UC-301", "screen": "Edit Profile Page", "path": [{"step_number": 1, "source_screen": "Edit Profile Page", "target_screen": "Edit Profile Page", "action": "Start", "element": "Initial screen"}]}, {"use_case_id": "UC-302", "screen": "Edit Product Page", "path": [{"step_number": 1, "source_screen": "Edit Profile Page", "target_screen": "User Profile Page", "action": "Saves profile updates", "element": "Save Changes <PERSON>"}, {"step_number": 2, "source_screen": "User Profile Page", "target_screen": "<PERSON><PERSON>", "action": "Logs out", "element": "User Account <PERSON>u"}, {"step_number": 3, "source_screen": "<PERSON><PERSON>", "target_screen": "Seller Dashboard", "action": "Logs in as <PERSON><PERSON>", "element": "<PERSON><PERSON>"}, {"step_number": 4, "source_screen": "Seller Dashboard", "target_screen": "Seller Products Page", "action": "Navigates to manage products", "element": "Navigation Menu"}, {"step_number": 5, "source_screen": "Seller Products Page", "target_screen": "Edit Product Page", "action": "Clicks 'Add Product'", "element": "Add Product Button"}]}, {"use_case_id": "UC-102", "screen": "<PERSON><PERSON>", "path": [{"step_number": 1, "source_screen": "Edit Profile Page", "target_screen": "User Profile Page", "action": "Saves profile updates", "element": "Save Changes <PERSON>"}, {"step_number": 2, "source_screen": "User Profile Page", "target_screen": "<PERSON><PERSON>", "action": "Logs out", "element": "User Account <PERSON>u"}]}, {"use_case_id": "UC-203", "screen": "Product Detail Page", "path": [{"step_number": 1, "source_screen": "<PERSON><PERSON>", "target_screen": "Home Page", "action": "Logs in as Customer", "element": "<PERSON><PERSON>"}, {"step_number": 2, "source_screen": "Home Page", "target_screen": "Product Detail Page", "action": "Clicks on a product", "element": "Product Cards"}]}, {"use_case_id": "UC-207", "screen": "Shopping Cart Page", "path": [{"step_number": 1, "source_screen": "Product Detail Page", "target_screen": "Shopping Cart Page", "action": "Adds product to cart and views cart", "element": "Add to Cart Button"}]}, {"use_case_id": "UC-305", "screen": "Seller Orders Page", "path": [{"step_number": 1, "source_screen": "Shopping Cart Page", "target_screen": "Home Page", "action": "Clicks 'Continue Shopping'", "element": "Continue Shopping Button"}, {"step_number": 2, "source_screen": "Home Page", "target_screen": "<PERSON><PERSON>", "action": "Clicks 'Login' in user menu", "element": "User Account <PERSON>u"}, {"step_number": 3, "source_screen": "<PERSON><PERSON>", "target_screen": "Seller Dashboard", "action": "Logs in as <PERSON><PERSON>", "element": "<PERSON><PERSON>"}, {"step_number": 4, "source_screen": "Seller Dashboard", "target_screen": "Seller Orders Page", "action": "Navigates to manage orders", "element": "Navigation Menu"}]}, {"use_case_id": "UC-211", "screen": "Customer Bookings Page", "path": [{"step_number": 1, "source_screen": "User Orders Page", "target_screen": "Product Detail Page", "action": "Clicks on a product in an order", "element": "Orders List"}, {"step_number": 2, "source_screen": "Product Detail Page", "target_screen": "Home Page", "action": "Clicks site logo to return home", "element": "Site Logo"}, {"step_number": 3, "source_screen": "Home Page", "target_screen": "<PERSON><PERSON>", "action": "Clicks 'Login' in user menu", "element": "User Account <PERSON>u"}, {"step_number": 4, "source_screen": "<PERSON><PERSON>", "target_screen": "Seller Dashboard", "action": "Logs in as <PERSON><PERSON>", "element": "<PERSON><PERSON>"}, {"step_number": 5, "source_screen": "Seller Dashboard", "target_screen": "Edit Profile Page", "action": "Navigates to manage shop profile", "element": "Navigation Menu"}, {"step_number": 6, "source_screen": "Edit Profile Page", "target_screen": "User Profile Page", "action": "Saves profile updates", "element": "Save Changes <PERSON>"}, {"step_number": 7, "source_screen": "User Profile Page", "target_screen": "Customer Bookings Page", "action": "Navigates to booking history", "element": "Account <PERSON><PERSON>"}]}, {"use_case_id": "UC-212", "screen": "Booking Details Page", "path": [{"step_number": 1, "source_screen": "Customer Bookings Page", "target_screen": "Booking Details Page", "action": "Views details of a booking", "element": "Booking Details Link"}]}]}