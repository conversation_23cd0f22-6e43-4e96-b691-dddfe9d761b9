"""
Path Processor Module for Pipeline
Processes and analyzes paths and workflows from document content
"""

import logging
import asyncio
from pathlib import Path
from typing import Dict, Any

from app.core.pipeline_registry import PipelineModule, PipelineModuleConfig, PipelineModuleType

logger = logging.getLogger(__name__)

class PathProcessorModule(PipelineModule):
    """Pipeline module for processing paths and workflows from documents"""
    
    def __init__(self):
        config = PipelineModuleConfig(
            id="path_processor",
            name="Path Processor",
            description="Processes and analyzes paths and workflows from document content",
            module_type=PipelineModuleType.PROCESSOR,
            version="1.0.0",
            dependencies=["document_processor", "business_flow_detector", "relevant_content_processor"],  # Depends on all previous modules
            input_types=["structured_json", "business_flow_json", "relevant_content_json"],
            output_types=["path_analysis_json", "workflow_json"],
            metadata={
                "ai_powered": True,
                "path_analysis": True,
                "workflow_generation": True,
                "output_format": "JSON"
            }
        )
        super().__init__(config)
        
        # Set up paths
        self.output_base_dir = Path("output")
        self.processor_script = Path("back_end/path_processor.py")

        # Set up config file path - use absolute path
        back_end_path = Path(__file__).parent.parent.parent.parent  # Go up 4 levels to back_end
        self.config_file = back_end_path / "document" / "gemini_config_pro.json"
        
        logger.info(f"PathProcessorModule initialized")
        logger.info(f"Output base directory: {self.output_base_dir}")
        logger.info(f"Processor script: {self.processor_script}")

    async def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input data for path processing"""
        try:
            # Check required fields
            if "document_id" not in input_data:
                logger.error("Missing required field: document_id")
                return False
                
            if "file_path" not in input_data:
                logger.error("Missing required field: file_path")
                return False
            
            document_id = input_data["document_id"]
            
            # Check if previous module outputs exist
            doc_processor_dir = self.output_base_dir / document_id / "document_processor"
            business_flow_dir = self.output_base_dir / document_id / "business_flows"
            relevant_content_dir = self.output_base_dir / document_id / "relevant_content_processor"
            
            # Check document processor outputs (required)
            merged_json = doc_processor_dir / "merged_output.json"
            if not merged_json.exists():
                logger.error(f"Required input file not found: {merged_json}")
                return False
            
            # Check other module outputs (optional but preferred)
            business_flows_exist = business_flow_dir.exists() and any(business_flow_dir.glob("*.json"))
            relevant_content_exists = relevant_content_dir.exists() and any(relevant_content_dir.glob("*.json"))
            
            # Check if processor script exists
            if not self.processor_script.exists():
                logger.error(f"Path processor script not found: {self.processor_script}")
                return False
            
            logger.info("Path processor input validation passed")
            logger.info(f"Business flows available: {business_flows_exist}")
            logger.info(f"Relevant content available: {relevant_content_exists}")
            return True
            
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            return False

    async def execute(self, input_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute path processing"""
        try:
            logger.info("PathProcessorModule.execute() called")
            logger.info(f"Input data: {input_data}")
            logger.info(f"Context: {context}")

            # Extract input parameters
            document_id = input_data["document_id"]
            file_path = Path(input_data["file_path"])

            logger.info(f"Processing document: {document_id}")
            logger.info(f"Original file: {file_path}")
            
            # Create output directory for this module
            doc_output_dir = self.output_base_dir / document_id / "path_processor"
            doc_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Input files from previous modules
            doc_processor_dir = self.output_base_dir / document_id / "document_processor"
            business_flow_dir = self.output_base_dir / document_id / "business_flows"
            relevant_content_dir = self.output_base_dir / document_id / "relevant_content_processor"
            
            input_json = doc_processor_dir / "merged_output.json"
            
            # Output files
            path_analysis_json = doc_output_dir / "path_analysis.json"
            workflow_json = doc_output_dir / "workflow.json"
            path_summary_json = doc_output_dir / "path_summary.json"
            
            logger.info(f"Input JSON: {input_json}")
            logger.info(f"Output directory: {doc_output_dir}")
            
            # Check if input file exists
            if not input_json.exists():
                raise FileNotFoundError(f"Input file not found: {input_json}")
            
            # Import and execute PathProcessor
            try:
                logger.info("Importing PathProcessor...")
                
                # Import the processor class
                import sys
                sys.path.append(str(Path("back_end").absolute()))
                
                from path_processor import ScreenPathGenerator
                logger.info("ScreenPathGenerator imported successfully")

                logger.info("Initializing ScreenPathGenerator...")
                # For now, process business flow 1 as example
                business_flow_number = 1

                # Create business flow subdirectory
                business_flow_subdir = doc_output_dir / f"business_flow_{business_flow_number}"
                business_flow_subdir.mkdir(parents=True, exist_ok=True)

                # New file paths - all files go into business_flow_X subdirectory
                flows_output_path = business_flow_subdir / "flows_data.json"
                screen_paths_output_path = business_flow_subdir / "screen_paths.json"

                processor = ScreenPathGenerator(
                    config_file=str(self.config_file),
                    relevant_content_base_dir=str(relevant_content_dir) if relevant_content_dir.exists() else str(doc_output_dir),
                    business_flows_dir=str(business_flow_dir) if business_flow_dir.exists() else "",
                    business_flow_number=business_flow_number,
                    flows_output_path=str(flows_output_path),
                    screen_paths_output_path=str(screen_paths_output_path),
                    base_output_dir=str(business_flow_subdir),  # Use subdirectory as base
                    count_token=False
                )
                logger.info("ScreenPathGenerator initialized")

                # Execute processing in thread pool to avoid blocking
                logger.info("Starting path processing in thread pool...")
                await asyncio.get_event_loop().run_in_executor(
                    None,
                    processor.execute
                )
                logger.info("Path processing completed")

            except ImportError as e:
                logger.error(f"Failed to import PathProcessor: {e}")
                return {
                    "success": False,
                    "error": f"PathProcessor not available: {e}",
                    "document_id": document_id
                }

            # Check outputs and prepare result
            outputs = {}
            summary = {
                "paths_analyzed": 0,
                "workflows_generated": 0,
                "total_steps": 0,
                "output_files": []
            }
            
            # Check for actual generated files in business flow subdirectories
            flows_found = 0
            paths_found = 0

            # Look for business flow subdirectories
            for bf_dir in doc_output_dir.iterdir():
                if bf_dir.is_dir() and bf_dir.name.startswith("business_flow_"):
                    bf_number = bf_dir.name.replace("business_flow_", "")

                    # Look for flows_data.json
                    flows_file = bf_dir / "flows_data.json"
                    if flows_file.exists():
                        flows_found += 1
                        outputs[f"flows_data_bf{bf_number}"] = str(flows_file)
                        summary["output_files"].append(str(flows_file))

                        # Try to read and count flows
                        try:
                            import json
                            with open(flows_file, 'r', encoding='utf-8') as f:
                                flow_data = json.load(f)
                                if isinstance(flow_data, dict) and "screen_flows" in flow_data:
                                    summary["workflows_generated"] += len(flow_data["screen_flows"])
                        except Exception as e:
                            logger.warning(f"Could not parse flow file {flows_file}: {e}")

                    # Look for screen_paths.json
                    paths_file = bf_dir / "screen_paths.json"
                    if paths_file.exists():
                        paths_found += 1
                        outputs[f"screen_paths_bf{bf_number}"] = str(paths_file)
                        summary["output_files"].append(str(paths_file))

                        # Try to read and count paths
                        try:
                            import json
                            with open(paths_file, 'r', encoding='utf-8') as f:
                                path_data = json.load(f)
                                if isinstance(path_data, dict) and "screen_paths" in path_data:
                                    summary["paths_analyzed"] += len(path_data["screen_paths"])
                                    # Count total steps across all paths
                                    for path_key, path_info in path_data["screen_paths"].items():
                                        if isinstance(path_info, dict) and "path" in path_info:
                                            summary["total_steps"] += len(path_info["path"])
                        except Exception as e:
                            logger.warning(f"Could not parse path file {paths_file}: {e}")

                    # Look for screen_variables.json
                    variables_file = bf_dir / "screen_variables.json"
                    if variables_file.exists():
                        outputs[f"variables_bf{bf_number}"] = str(variables_file)
                        summary["output_files"].append(str(variables_file))

                    # Look for screen_flow_graph.png in business flow directory
                    screen_flow_graph = bf_dir / "screen_flow_graph.png"
                    if screen_flow_graph.exists():
                        outputs[f"screen_flow_graph_bf{bf_number}"] = str(screen_flow_graph)
                        summary["output_files"].append(str(screen_flow_graph))
            
            # Determine success
            success = len(outputs) > 0
            
            if success:
                message = f"Successfully analyzed {summary['paths_analyzed']} paths, generated {summary['workflows_generated']} workflows with {summary['total_steps']} total steps"
                logger.info(f"Path processing completed: {document_id}")
            else:
                message = "No paths or workflows processed"
                logger.warning(f"No paths or workflows processed for: {document_id}")

            result = {
                "success": success,
                "document_id": document_id,
                "input_file": str(input_json),
                "output_directory": str(doc_output_dir),
                "output_files": outputs,
                "summary": summary,
                "message": message
            }
            
            logger.info(f"Path processing completed: {document_id}")
            return result

        except Exception as e:
            logger.error(f"Path processing failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "document_id": input_data.get("document_id", "unknown")
            }

    async def get_status(self) -> Dict[str, Any]:
        """Get current module status"""
        return {
            "module_id": self.config.id,
            "status": "ready",
            "version": self.config.version,
            "processor_script_exists": self.processor_script.exists(),
            "output_directory": str(self.output_base_dir)
        }
