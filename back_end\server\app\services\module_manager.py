"""
Module Manager Service
Manages pipeline module lifecycle and registration
"""

import logging
from typing import Dict, Any, List, Optional

from app.core.pipeline_registry import pipeline_registry, PipelineModule
from app.modules import create_document_processor_module

logger = logging.getLogger(__name__)

class ModuleManager:
    """Service for managing pipeline modules"""
    
    def __init__(self):
        self.initialized = False
        self.logger = logging.getLogger("module_manager")
    
    async def initialize(self):
        """Initialize all pipeline modules"""
        if self.initialized:
            self.logger.warning("Module manager already initialized")
            return
        
        try:
            self.logger.info("Initializing pipeline modules...")
            
            # Register document processor module
            await self._register_document_processor()

            # Register business flow detector module
            await self._register_business_flow_detector()

            # Register relevant content processor module
            await self._register_relevant_content_processor()

            # Register path processor module
            await self._register_path_processor()

            # Register test case CSV generator module
            await self._register_test_case_csv_generator()

            # Register generate test case module
            await self._register_gen_test_case()

            # Validate pipeline after all modules are registered
            validation_result = await pipeline_registry.validate_pipeline()
            if validation_result["valid"]:
                self.logger.info(f"Pipeline validation successful: {validation_result['module_count']} modules registered")
            else:
                self.logger.warning(f"Pipeline validation issues: {validation_result['issues']}")
            
            self.initialized = True
            self.logger.info("Module manager initialization completed")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize module manager: {e}")
            raise
    
    async def _register_document_processor(self):
        """Register the document processor module"""
        try:
            module = create_document_processor_module()
            pipeline_registry.register_module(module)
            self.logger.info("Document processor module registered successfully")
        except Exception as e:
            self.logger.error(f"Failed to register document processor module: {e}")
            raise

    async def _register_business_flow_detector(self):
        """Register business flow detector module"""
        try:
            from app.modules.business_flow_detector_module import BusinessFlowDetectorModule
            module = BusinessFlowDetectorModule()
            pipeline_registry.register_module(module)
            self.logger.info("Business flow detector module registered successfully")
        except Exception as e:
            self.logger.error(f"Failed to register business flow detector module: {e}")
            raise

    async def _register_relevant_content_processor(self):
        """Register relevant content processor module"""
        try:
            from app.modules.relevant_content_processor_module import RelevantContentProcessorModule
            module = RelevantContentProcessorModule()
            pipeline_registry.register_module(module)
            self.logger.info("Relevant content processor module registered successfully")
        except Exception as e:
            self.logger.error(f"Failed to register relevant content processor module: {e}")
            raise

    async def _register_path_processor(self):
        """Register path processor module"""
        try:
            from app.modules.path_processor_module import PathProcessorModule
            module = PathProcessorModule()
            pipeline_registry.register_module(module)
            self.logger.info("Path processor module registered successfully")
        except Exception as e:
            self.logger.error(f"Failed to register path processor module: {e}")
            raise

    async def _register_test_case_csv_generator(self):
        """Register test case CSV generator module"""
        try:
            from app.modules.test_case_csv_generator_module import TestCaseCsvGeneratorModule
            module = TestCaseCsvGeneratorModule({})
            pipeline_registry.register_module(module)
            self.logger.info("Test case CSV generator module registered successfully")
        except Exception as e:
            self.logger.error(f"Failed to register test case CSV generator module: {e}")
            raise

    async def _register_gen_test_case(self):
        """Register generate test case module"""
        try:
            from app.modules.gen_test_case_module import GenTestCaseModule
            module = GenTestCaseModule({})
            pipeline_registry.register_module(module)
            self.logger.info("Generate test case module registered successfully")
        except Exception as e:
            self.logger.error(f"Failed to register generate test case module: {e}")
            raise

    async def cleanup(self):
        """Cleanup module manager"""
        if not self.initialized:
            return
        
        try:
            self.logger.info("Cleaning up module manager...")
            # Perform any necessary cleanup
            self.initialized = False
            self.logger.info("Module manager cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during module manager cleanup: {e}")
    
    def get_registered_modules(self) -> List[Dict[str, Any]]:
        """Get list of all registered modules"""
        return pipeline_registry.list_modules()
    
    def get_module(self, module_id: str) -> Optional[PipelineModule]:
        """Get a specific module by ID"""
        return pipeline_registry.get_module(module_id)
    
    async def execute_module(self, module_id: str, input_data: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a specific module"""
        if context is None:
            context = {}

        self.logger.info(f"🔍 Looking for module: {module_id}")
        module = pipeline_registry.get_module(module_id)
        if not module:
            self.logger.error(f"❌ Module {module_id} not found in registry")
            raise ValueError(f"Module {module_id} not found")

        self.logger.info(f"✅ Found module: {module_id} - {module.config.name}")

        # Validate input
        self.logger.info(f"🔧 Validating input for module: {module_id}")
        self.logger.info(f"📋 Input data: {input_data}")

        if not await module.validate_input(input_data):
            self.logger.error(f"❌ Invalid input data for module {module_id}")
            raise ValueError(f"Invalid input data for module {module_id}")

        self.logger.info(f"✅ Input validation passed for module: {module_id}")

        # Execute module
        self.logger.info(f"⚡ Starting execution of module: {module_id}")
        try:
            result = await module.execute(input_data, context)
            self.logger.info(f"✅ Module {module_id} execution completed successfully")
            self.logger.info(f"📊 Result: {result}")
            return result
        except Exception as e:
            self.logger.error(f"❌ Module {module_id} execution failed: {e}")
            self.logger.error(f"💥 Exception type: {type(e).__name__}")
            raise
    
    async def get_module_status(self, module_id: str) -> Dict[str, Any]:
        """Get status of a specific module"""
        module = pipeline_registry.get_module(module_id)
        if not module:
            return {"error": f"Module {module_id} not found"}
        
        return await module.get_status()

# Global module manager instance
module_manager = ModuleManager()
