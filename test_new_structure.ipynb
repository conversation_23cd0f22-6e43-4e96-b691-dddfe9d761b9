{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LLM Functional Test - New Multi-Business Flow Structure\n", "\n", "This notebook demonstrates the new structure that supports multiple business flows with organized folder structure."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "from pathlib import Path\n", "from back_end.BusinessFlowDetector import GenBusinessFlow\n", "# from back_end.ExtractAndProcessToJson import DiagramProcessor\n", "from back_end.test_case_evaluator import TestCaseEvaluator\n", "from back_end.document_processor import DocumentProcessor\n", "from back_end.path_to_csv import ScreenTestCaseGenerator\n", "from back_end.relevant_content_processor import RelevantContentProcessor\n", "from back_end.path_processor import ScreenPathGenerator\n", "from back_end.gen_test_case import GenTestCaseBussinessFlow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get PDF path from user\n", "pdf_path = input(\"Enter the PDF path: \")\n", "pdf_path = Path(pdf_path)\n", "pdf_name = pdf_path.stem\n", "parent_dir = Path('./back_end/output')\n", "\n", "print(f\"Processing PDF: {pdf_name}\")\n", "print(f\"Output directory: {parent_dir / pdf_name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ===== PATH CONFIGURATION (NEW STRUCTURE) =====\n", "\n", "# Base directories\n", "document_processor_dir = parent_dir / pdf_name / 'document_processor'\n", "merged_json = document_processor_dir / 'merged_output.json'\n", "analyzed_json = document_processor_dir / 'description_output.json'\n", "\n", "pdf_file = pdf_path\n", "output_directory = parent_dir / pdf_name / 'diagrams'\n", "config_path_flash = \"back_end/document/gemini_config_flash.json\"\n", "config_path_pro = \"back_end/document/gemini_config_pro.json\"\n", "doc_processor_file = document_processor_dir / 'merged_output.json'\n", "\n", "# Business flows directory\n", "output_business_flow_directory = parent_dir / pdf_name / 'business_flows'\n", "\n", "# Relevant content processor directory (contains business_flow_X subdirs + shared files)\n", "relevant_content_processor_dir = parent_dir / pdf_name / 'relevant_content_processor'\n", "# Shared files at base level\n", "screen_graph_json = relevant_content_processor_dir / 'screen_graph.json'\n", "\n", "# Path processor directory (contains business_flow_X subdirs + shared files)\n", "path_processor_dir = parent_dir / pdf_name / 'path_processor'\n", "# Shared files at base level\n", "variables_output_path = path_processor_dir / 'screen_variables.json'\n", "graph_image_output_path = path_processor_dir / 'screen_flow_graph.png'\n", "\n", "# Test case generator directory (will contain business_flow_X subdirs)\n", "test_case_generator_dir = parent_dir / pdf_name / 'test_case_csv_generator'\n", "\n", "# Final test case output directory (will contain business_flow_X subdirs)\n", "test_case_output_dir = parent_dir / pdf_name / 'TestCase_Output'\n", "\n", "print(\"✅ Path configuration completed\")\n", "print(f\"📁 Main output directory: {parent_dir / pdf_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Document Processing\n", "Extract and process content from PDF documents."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["processor = DocumentProcessor(\n", "    pdf_path=pdf_file,\n", "    use_auto_toc=True, \n", "    output_dir=str(document_processor_dir),\n", "    merged_json=str(merged_json),\n", "    config_file=config_path_flash,\n", "    analyzed_json=str(analyzed_json),\n", "    count_token=True\n", ")\n", "\n", "# Ch<PERSON>y xử lý với verbose output\n", "processor.execute(verbose=False, skip_llm=False)\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Business Flow Generation\n", "Generate business flows from the processed documents."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Business flow generation\n", "if not os.path.exists(doc_processor_file):\n", "    print(f\"❌ Error: Input file not found at {doc_processor_file}\")\n", "else:\n", "    print(\"🔄 Starting business flow generation...\")\n", "    flow_generator = GenBusinessFlow(\n", "        input_json_file=str(doc_processor_file),\n", "        business_flow_dir=str(output_business_flow_directory),\n", "        config_file=config_path_flash,\n", "        count_token=True\n", "    )\n", "    generated_flows = flow_generator.execute()\n", "\n", "    if generated_flows:\n", "        print(\"✅ Successfully generated business flows.\")\n", "    else:\n", "        print(\"❌ Failed to generate business flows or no flows were produced.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Relevant Content Processing\n", "Process relevant content for all business flows using the new multi-flow structure."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Relevant content processing (NEW STRUCTURE - processes all business flows)\n", "print(\"🔄 Starting relevant content processing for all business flows...\")\n", "processor = RelevantContentProcessor(\n", "    description_json=str(analyzed_json),\n", "    merged_json=str(merged_json), \n", "    config_file=config_path_pro,\n", "    business_flows_dir=str(output_business_flow_directory),  \n", "    base_output_dir=str(relevant_content_processor_dir), \n", "    max_workers=5,\n", "    enable_caching=True               \n", ")\n", "processor.execute_all_flows()\n", "print(\"✅ Relevant content processing completed for all business flows\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Screen Path Generation\n", "Generate screen paths for all business flows using the new multi-flow structure."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Screen path generation (NEW STRUCTURE - processes all business flows)\n", "print(\"🔄 Starting screen path generation for all business flows...\")\n", "results = ScreenPathGenerator.process_all_business_flows(\n", "    config_file=config_path_pro,\n", "    relevant_content_base_dir=str(relevant_content_processor_dir),\n", "    business_flows_dir=str(output_business_flow_directory),\n", "    base_output_dir=str(path_processor_dir),\n", "    count_token=True\n", ")\n", "print(f\"✅ Screen path generation completed for {len(results)} business flows\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Test Case CSV Generation\n", "Generate test case CSV files for all business flows using the new multi-flow structure."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test case CSV generation (NEW STRUCTURE - processes all business flows)\n", "print(\"🔄 Starting test case CSV generation for all business flows...\")\n", "csv_results = ScreenTestCaseGenerator.process_from_base_dir(\n", "    base_dir=str(parent_dir / pdf_name),\n", "    output_dir=str(test_case_generator_dir)\n", ")\n", "print(f\"✅ Test case CSV generation completed for {len(csv_results)} business flows\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Final Test Case Generation\n", "Generate final test case JSON files from the CSV files for all business flows."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final test case generation (NEW STRUCTURE - processes all business flows)\n", "print(\"🔄 Starting final test case generation for all business flows...\")\n", "\n", "# Use the new enhanced GenTestCaseBussinessFlow API\n", "final_results = GenTestCaseBussinessFlow.process_from_base_dir(\n", "    base_dir=str(parent_dir / pdf_name),\n", "    output_dir=str(test_case_output_dir),\n", "    config_file=config_path_pro,\n", "    count_token=True\n", ")\n", "\n", "if final_results:\n", "    print(\"✅ Final test case generation completed\")\n", "    print(f\"📊 Processed {len(final_results)} business flows\")\n", "    \n", "    total_json_files = 0\n", "    successful_flows = [r for r in final_results if r['success']]\n", "    \n", "    for result in final_results:\n", "        status = \"✅\" if result['success'] else \"❌\"\n", "        bf_num = result['business_flow_number']\n", "        print(f\"  {status} Business Flow {bf_num}\")\n", "        \n", "        if result['success']:\n", "            # Fix: processed_files is a list, so get its length\n", "            processed_files = result.get('processed_files', [])\n", "            total_files = result.get('total_files', 0)\n", "            \n", "            # Count the number of processed files\n", "            processed_count = len(processed_files) if isinstance(processed_files, list) else processed_files\n", "            total_json_files += processed_count\n", "            \n", "            print(f\"    📄 Generated {processed_count}/{total_files} JSON test case files\")\n", "            print(f\"    📁 Output: {result['output_directory']}\")\n", "    \n", "    print(f\"\\n🎉 Summary:\")\n", "    print(f\"   📈 Total JSON test case files: {total_json_files}\")\n", "    print(f\"   ✅ Successful business flows: {len(successful_flows)}/{len(final_results)}\")\n", "else:\n", "    print(\"❌ Final test case generation failed\")\n", "\n", "print(\"\\n✅ Final test case generation completed for all business flows\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Test Case Evaluation\n", "Evaluate the generated test cases against benchmarks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test case evaluation\n", "print(\"🔄 Starting test case evaluation...\")\n", "benchmark = \"back_end/document/evaluation_benchmark.json\"\n", "evaluate_output = parent_dir / pdf_name / 'evaluate_output'\n", "\n", "evaluator = TestCaseEvaluator(\n", "    test_case_dir=str(test_case_output_dir),\n", "    benchmark_file=benchmark,\n", "    original_doc_file=str(merged_json),\n", "    output_dir=str(evaluate_output),\n", "    config_file=config_path_flash,\n", "    rotate_api_key=True\n", ")\n", "evaluator.run()\n", "print(\"✅ Test case evaluation completed\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}