{"screen_flows": {"Home Page": {"screen_name": "Home Page", "use_case_related": null, "next_screen": "<PERSON><PERSON>", "before_screen": "<PERSON><PERSON>"}, "Product Detail Page": {"screen_name": "Product Detail Page", "use_case_related": "UC-203", "next_screen": "Shopping Cart Page", "before_screen": "Home Page"}, "Search Results Page": {"screen_name": "Search Results Page", "use_case_related": null, "next_screen": "Product Detail Page", "before_screen": "Home Page"}, "About Us Page": {"screen_name": "About Us Page", "use_case_related": null, "next_screen": null, "before_screen": "Home Page"}, "Contact Page": {"screen_name": "Contact Page", "use_case_related": null, "next_screen": null, "before_screen": "Home Page"}, "Collaborations Page": {"screen_name": "Collaborations Page", "use_case_related": null, "next_screen": null, "before_screen": "Home Page"}, "Login Page": {"screen_name": "<PERSON><PERSON>", "use_case_related": "UC-102", "next_screen": "Home Page", "before_screen": "Home Page"}, "Customer Registration Page": {"screen_name": "Customer Registration Page", "use_case_related": "UC-101", "next_screen": "<PERSON><PERSON>", "before_screen": "Home Page"}, "Seller Registration Page": {"screen_name": "Seller Registration Page", "use_case_related": null, "next_screen": "<PERSON><PERSON>", "before_screen": "<PERSON><PERSON>"}, "Shopping Cart Page": {"screen_name": "Shopping Cart Page", "use_case_related": "UC-207", "next_screen": "User Orders Page", "before_screen": "Home Page"}, "Customer Bookings Page": {"screen_name": "Customer Bookings Page", "use_case_related": "UC-211", "next_screen": "Booking Details Page", "before_screen": "User Profile Page"}, "Booking Details Page": {"screen_name": "Booking Details Page", "use_case_related": "UC-212", "next_screen": "Customer Bookings Page", "before_screen": "Customer Bookings Page"}, "Create Booking Page": {"screen_name": "Create Booking Page", "use_case_related": null, "next_screen": "Customer Bookings Page", "before_screen": "Customer Bookings Page"}, "User Profile Page": {"screen_name": "User Profile Page", "use_case_related": null, "next_screen": "Edit Profile Page", "before_screen": "Edit Profile Page"}, "Edit Profile Page": {"screen_name": "Edit Profile Page", "use_case_related": "UC-301", "next_screen": "User Profile Page", "before_screen": "User Profile Page"}, "Change Password Page": {"screen_name": "Change Password Page", "use_case_related": null, "next_screen": "User Profile Page", "before_screen": "User Profile Page"}, "User Orders Page": {"screen_name": "User Orders Page", "use_case_related": "UC-208", "next_screen": "Product Detail Page", "before_screen": "Shopping Cart Page"}, "Seller Dashboard": {"screen_name": "Seller Dashboard", "use_case_related": null, "next_screen": "Seller Products Page", "before_screen": "<PERSON><PERSON>"}, "Seller Products Page": {"screen_name": "Seller Products Page", "use_case_related": null, "next_screen": "Edit Product Page", "before_screen": "Seller Dashboard"}, "Edit Product Page": {"screen_name": "Edit Product Page", "use_case_related": "UC-302", "next_screen": "Seller Products Page", "before_screen": "Seller Products Page"}, "Seller Orders Page": {"screen_name": "Seller Orders Page", "use_case_related": "UC-305", "next_screen": "Seller Order Details Page", "before_screen": "Seller Dashboard"}, "Seller Order Details Page": {"screen_name": "Seller Order Details Page", "use_case_related": null, "next_screen": "Seller Orders Page", "before_screen": "Seller Orders Page"}, "Seller Bookings Page": {"screen_name": "Se<PERSON> Bookings Page", "use_case_related": null, "next_screen": "Seller Booking Details Page", "before_screen": "Seller Dashboard"}, "Seller Booking Details Page": {"screen_name": "Seller Booking Details Page", "use_case_related": null, "next_screen": "Se<PERSON> Bookings Page", "before_screen": "Se<PERSON> Bookings Page"}, "Admin Dashboard": {"screen_name": "Admin Dashboard", "use_case_related": null, "next_screen": "Admin Products Page", "before_screen": "<PERSON><PERSON>"}, "Admin Products Page": {"screen_name": "Admin Products Page", "use_case_related": null, "next_screen": null, "before_screen": "Admin Dashboard"}, "Admin Pending Products Page": {"screen_name": "Admin Pending Products Page", "use_case_related": "UC-405", "next_screen": "Admin Products Page", "before_screen": "Admin Dashboard"}, "Admin Categories Page": {"screen_name": "Admin Categories Page", "use_case_related": null, "next_screen": null, "before_screen": "Admin Dashboard"}, "Admin Users Page": {"screen_name": "Admin Users Page", "use_case_related": null, "next_screen": null, "before_screen": "Admin Dashboard"}, "Admin Orders Page": {"screen_name": "Admin Orders Page", "use_case_related": null, "next_screen": "Admin Order Details Page", "before_screen": "Admin Dashboard"}, "Admin Order Details Page": {"screen_name": "Admin Order Details Page", "use_case_related": null, "next_screen": "Admin Orders Page", "before_screen": "Admin Orders Page"}, "Admin Sale Codes Page": {"screen_name": "Admin Sale Codes Page", "use_case_related": null, "next_screen": null, "before_screen": "Admin Dashboard"}, "Access Denied Page": {"screen_name": "Access Denied Page", "use_case_related": null, "next_screen": "Home Page", "before_screen": null}, "Error Page": {"screen_name": "Error <PERSON>", "use_case_related": null, "next_screen": "Home Page", "before_screen": null}}, "use_case_flows": [{"number": "1", "id": "UC-301", "screen_belong": "Edit Profile Page", "description": "Allows a Seller to update their profile information and shop details including shop name, description, contact information, and shop logo/banner images.", "Role": "<PERSON><PERSON>"}, {"number": "2", "id": "UC-302", "screen_belong": "Edit Product Page", "description": "Allows a Seller to perform full CRUD operations on their products including creating new products, editing existing ones, uploading images, setting prices, and managing inventory.", "Role": "<PERSON><PERSON>"}, {"number": "3", "id": "UC-101", "screen_belong": "Customer Registration Page", "description": "Allows an Anonymous User to register for a new account on the SASUCare platform by providing necessary details such as email, password, first name, last name, and optionally a shop name if registering as a Seller.", "Role": "Anonymous User"}, {"number": "4", "id": "UC-102", "screen_belong": "<PERSON><PERSON>", "description": "Allows a Registered User to authenticate and access the SASUCare platform by providing their email and password.", "Role": "Registered User (Seller or Customer)"}, {"number": "5", "id": "UC-203", "screen_belong": "Product Detail Page", "description": "Allows Customer to add a selected product to their shopping cart. The system validates product availability, checks stock quantity, and updates the cart with the selected item and quantity.", "Role": "Customer"}, {"number": "6", "id": "UC-207", "screen_belong": "Shopping Cart Page", "description": "Allows Customer to complete their purchase by providing shipping information, selecting payment method, and confirming the order.", "Role": "Customer"}, {"number": "7", "id": "UC-305", "screen_belong": "Seller Orders Page", "description": "Allows a Seller to view and process orders containing their products, update order status, add tracking information, and communicate with customers about order progress.", "Role": "<PERSON><PERSON>"}, {"number": "8", "id": "UC-208", "screen_belong": "User Orders Page", "description": "Allows Customer to view all their past orders with current status information, order details, tracking information, and the ability to reorder or review products.", "Role": "Customer"}, {"number": "9", "id": "UC-211", "screen_belong": "Customer Bookings Page", "description": "Allows Customer to view all their past and current service bookings with real-time status information, booking details, and the ability to manage upcoming bookings.", "Role": "Customer"}, {"number": "10", "id": "UC-212", "screen_belong": "Booking Details Page", "description": "Allows Customer to cancel an existing service booking based on the cancellation policy, with automatic refund processing and service provider notification.", "Role": "Customer"}, {"number": "11", "id": "UC-405", "screen_belong": "Admin Pending Products Page", "description": "Allows an Administrator to oversee and manage all products on the platform including approving, rejecting, editing, or removing products from any seller.", "Role": "Administrator"}]}