{"graph": {"nodes": [{"id": "home_page", "name": "Home Page", "type": "screen"}, {"id": "product_detail_page", "name": "Product Detail Page", "type": "screen"}, {"id": "search_results_page", "name": "Search Results Page", "type": "screen"}, {"id": "about_us_page", "name": "About Us Page", "type": "screen"}, {"id": "contact_page", "name": "Contact Page", "type": "screen"}, {"id": "collaborations_page", "name": "Collaborations Page", "type": "screen"}, {"id": "login_page", "name": "<PERSON><PERSON>", "type": "screen"}, {"id": "customer_registration_page", "name": "Customer Registration Page", "type": "screen"}, {"id": "seller_registration_page", "name": "Seller Registration Page", "type": "screen"}, {"id": "shopping_cart_page", "name": "Shopping Cart Page", "type": "screen"}, {"id": "customer_bookings_page", "name": "Customer Bookings Page", "type": "screen"}, {"id": "booking_details_page", "name": "Booking Details Page", "type": "screen"}, {"id": "create_booking_page", "name": "Create Booking Page", "type": "screen"}, {"id": "user_profile_page", "name": "User Profile Page", "type": "screen"}, {"id": "edit_profile_page", "name": "Edit Profile Page", "type": "screen"}, {"id": "change_password_page", "name": "Change Password Page", "type": "screen"}, {"id": "user_orders_page", "name": "User Orders Page", "type": "screen"}, {"id": "seller_dashboard", "name": "Seller Dashboard", "type": "screen"}, {"id": "seller_products_page", "name": "Seller Products Page", "type": "screen"}, {"id": "edit_product_page", "name": "Edit Product Page", "type": "screen"}, {"id": "seller_orders_page", "name": "Seller Orders Page", "type": "screen"}, {"id": "seller_order_details_page", "name": "Seller Order Details Page", "type": "screen"}, {"id": "seller_bookings_page", "name": "Se<PERSON> Bookings Page", "type": "screen"}, {"id": "seller_booking_details_page", "name": "Seller Booking Details Page", "type": "screen"}, {"id": "admin_dashboard", "name": "Admin Dashboard", "type": "screen"}, {"id": "admin_products_page", "name": "Admin Products Page", "type": "screen"}, {"id": "admin_pending_products_page", "name": "Admin Pending Products Page", "type": "screen"}, {"id": "admin_categories_page", "name": "Admin Categories Page", "type": "screen"}, {"id": "admin_users_page", "name": "Admin Users Page", "type": "screen"}, {"id": "admin_orders_page", "name": "Admin Orders Page", "type": "screen"}, {"id": "admin_order_details_page", "name": "Admin Order Details Page", "type": "screen"}, {"id": "admin_sale_codes_page", "name": "Admin Sale Codes Page", "type": "screen"}, {"id": "access_denied_page", "name": "Access Denied Page", "type": "screen"}, {"id": "error_page", "name": "Error <PERSON>", "type": "screen"}], "edges": [{"source": "home_page", "target": "login_page", "action": "clicks 'Login'", "element": "User Account <PERSON>u"}, {"source": "home_page", "target": "customer_registration_page", "action": "clicks 'Sign Up'", "element": "Main Navigation"}, {"source": "home_page", "target": "search_results_page", "action": "submits search", "element": "Search Bar"}, {"source": "home_page", "target": "product_detail_page", "action": "clicks product", "element": "Product Cards"}, {"source": "home_page", "target": "shopping_cart_page", "action": "clicks cart icon", "element": "Shopping Cart Icon"}, {"source": "home_page", "target": "about_us_page", "action": "clicks navigation link", "element": "Main Navigation"}, {"source": "home_page", "target": "contact_page", "action": "clicks navigation link", "element": "Main Navigation"}, {"source": "home_page", "target": "collaborations_page", "action": "clicks navigation link", "element": "Main Navigation"}, {"source": "login_page", "target": "home_page", "action": "logs in as Customer", "element": "<PERSON><PERSON>"}, {"source": "login_page", "target": "seller_dashboard", "action": "logs in as Seller", "element": "<PERSON><PERSON>"}, {"source": "login_page", "target": "admin_dashboard", "action": "logs in as Admin", "element": "<PERSON><PERSON>"}, {"source": "login_page", "target": "customer_registration_page", "action": "clicks registration link", "element": "Registration Links"}, {"source": "login_page", "target": "seller_registration_page", "action": "clicks registration link", "element": "Registration Links"}, {"source": "customer_registration_page", "target": "login_page", "action": "submits registration", "element": "Register <PERSON>"}, {"source": "seller_registration_page", "target": "login_page", "action": "submits registration", "element": "Register <PERSON>"}, {"source": "search_results_page", "target": "product_detail_page", "action": "clicks product", "element": "Results Grid"}, {"source": "shopping_cart_page", "target": "home_page", "action": "continues shopping", "element": "Continue Shopping Button"}, {"source": "shopping_cart_page", "target": "user_orders_page", "action": "places order", "element": "Checkout <PERSON><PERSON>"}, {"source": "home_page", "target": "user_profile_page", "action": "accesses account menu", "element": "User Account <PERSON>u"}, {"source": "user_profile_page", "target": "user_orders_page", "action": "clicks 'Order History'", "element": "Account <PERSON><PERSON>"}, {"source": "user_profile_page", "target": "customer_bookings_page", "action": "clicks 'Booking History'", "element": "Account <PERSON><PERSON>"}, {"source": "user_profile_page", "target": "edit_profile_page", "action": "clicks to edit profile", "element": "Account <PERSON><PERSON>"}, {"source": "user_profile_page", "target": "change_password_page", "action": "clicks to change password", "element": "Account <PERSON><PERSON>"}, {"source": "edit_profile_page", "target": "user_profile_page", "action": "saves profile", "element": "Save Changes <PERSON>"}, {"source": "edit_profile_page", "target": "user_profile_page", "action": "cancels edit", "element": "<PERSON><PERSON>"}, {"source": "change_password_page", "target": "user_profile_page", "action": "updates password", "element": "Change Password <PERSON>"}, {"source": "customer_bookings_page", "target": "booking_details_page", "action": "views booking details", "element": "Booking Details Link"}, {"source": "customer_bookings_page", "target": "create_booking_page", "action": "creates new booking", "element": "Create New Booking"}, {"source": "create_booking_page", "target": "customer_bookings_page", "action": "submits booking", "element": "Submit Booking Button"}, {"source": "booking_details_page", "target": "customer_bookings_page", "action": "cancels booking", "element": "Action Buttons"}, {"source": "booking_details_page", "target": "customer_bookings_page", "action": "returns to bookings list", "element": "Back to Bookings"}, {"source": "seller_dashboard", "target": "seller_products_page", "action": "navigates to manage products", "element": "Navigation Menu"}, {"source": "seller_dashboard", "target": "seller_orders_page", "action": "navigates to manage orders", "element": "Navigation Menu"}, {"source": "seller_dashboard", "target": "seller_bookings_page", "action": "navigates to manage bookings", "element": "Navigation Menu"}, {"source": "seller_products_page", "target": "edit_product_page", "action": "edits product", "element": "Product Actions"}, {"source": "seller_products_page", "target": "edit_product_page", "action": "adds new product", "element": "Add Product Button"}, {"source": "edit_product_page", "target": "seller_products_page", "action": "saves product changes", "element": "Save/Update But<PERSON>"}, {"source": "seller_orders_page", "target": "seller_order_details_page", "action": "views order details", "element": "Order Details Link"}, {"source": "seller_order_details_page", "target": "seller_orders_page", "action": "returns to orders list", "element": "Back to Orders"}, {"source": "seller_bookings_page", "target": "seller_booking_details_page", "action": "views booking details", "element": "Booking Details Link"}, {"source": "seller_booking_details_page", "target": "seller_bookings_page", "action": "returns to bookings list", "element": "Back to Bookings"}, {"source": "admin_dashboard", "target": "admin_products_page", "action": "navigates to manage all products", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_pending_products_page", "action": "navigates to pending products", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_categories_page", "action": "navigates to manage categories", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_users_page", "action": "navigates to manage users", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_orders_page", "action": "navigates to manage all orders", "element": "Navigation Menu"}, {"source": "admin_dashboard", "target": "admin_sale_codes_page", "action": "navigates to manage sale codes", "element": "Navigation Menu"}, {"source": "admin_orders_page", "target": "admin_order_details_page", "action": "views order details", "element": "Order Details Link"}, {"source": "admin_order_details_page", "target": "admin_orders_page", "action": "returns to orders list", "element": "Admin Actions"}, {"source": "error_page", "target": "home_page", "action": "returns to home", "element": "Home Page Link"}, {"source": "access_denied_page", "target": "home_page", "action": "returns to accessible area", "element": "Navigation Options"}, {"source": "access_denied_page", "target": "login_page", "action": "attempts to login", "element": "Login Suggestion"}]}}