{"screen_definitions": [{"screen_name": "Home Page", "variables": {"search_query": {"valid": ["existing_product_name", "partial_product_name"], "invalid": [""]}, "button_action": {"valid": ["Search"], "invalid": []}}}, {"screen_name": "Product Detail Page", "variables": {"quantity": {"valid": ["1", "1-99"], "invalid": ["0", "-1", ">99", "exceeds_stock"]}, "product_options": {"valid": ["valid_option"], "invalid": ["invalid_option"]}, "button_action": {"valid": ["Add to Cart"], "invalid": []}}}, {"screen_name": "Search Results Page", "variables": {"filter_criteria": {"valid": ["valid_filter"], "invalid": []}, "button_action": {"valid": ["Apply Filters"], "invalid": []}}}, {"screen_name": "About Us Page", "variables": {}}, {"screen_name": "Contact Page", "variables": {}}, {"screen_name": "Collaborations Page", "variables": {}}, {"screen_name": "<PERSON><PERSON>", "variables": {"email": {"valid": ["<EMAIL>"], "invalid": ["not_an_email", "<EMAIL>", "<EMAIL>"]}, "password": {"valid": ["correct_password"], "invalid": ["incorrect_password"]}, "captcha": {"valid": ["correct_captcha"], "invalid": ["incorrect_captcha"]}, "button_action": {"valid": ["<PERSON><PERSON>"], "invalid": []}}}, {"screen_name": "Customer Registration Page", "variables": {"email": {"valid": ["<EMAIL>"], "invalid": ["invalid_format", "<EMAIL>", "<EMAIL>"]}, "password": {"valid": ["ValidPass1!"], "invalid": ["short", "no_uppercase_1!", "NO_LOWERCASE_1!", "NoSpecialChar1", "NoNumberChar!", "contains_email_or_name"]}, "confirm_password": {"valid": ["matches_password"], "invalid": ["does_not_match_password"]}, "first_name": {"valid": ["ValidFirstName"], "invalid": [""]}, "last_name": {"valid": ["ValidLastName"], "invalid": [""]}, "terms_and_conditions_checkbox": {"valid": ["checked"], "invalid": ["unchecked"]}, "privacy_policy_acknowledgment": {"valid": ["checked"], "invalid": ["unchecked"]}, "marketing_consent": {"valid": ["checked", "unchecked"], "invalid": []}, "button_action": {"valid": ["Register"], "invalid": []}}}, {"screen_name": "Seller Registration Page", "variables": {"email": {"valid": ["<EMAIL>"], "invalid": ["invalid_format", "<EMAIL>", "<EMAIL>"]}, "password": {"valid": ["ValidPass1!"], "invalid": ["short", "no_uppercase_1!", "NO_LOWERCASE_1!", "NoSpecialChar1", "NoNumberChar!", "contains_email_or_name"]}, "confirm_password": {"valid": ["matches_password"], "invalid": ["does_not_match_password"]}, "first_name": {"valid": ["ValidFirstName"], "invalid": [""]}, "last_name": {"valid": ["ValidLastName"], "invalid": [""]}, "shop_name": {"valid": ["3-50_alphanumeric_unique_name"], "invalid": ["s", "name_over_50_characters_long_for_testing_purposes", "duplicate_shop_name", "profane_name"]}, "terms_and_conditions_checkbox": {"valid": ["checked"], "invalid": ["unchecked"]}, "privacy_policy_acknowledgment": {"valid": ["checked"], "invalid": ["unchecked"]}, "marketing_consent": {"valid": ["checked", "unchecked"], "invalid": []}, "button_action": {"valid": ["Register"], "invalid": []}}}, {"screen_name": "Shopping Cart Page", "variables": {"street_address": {"valid": ["5-100 alphanumeric chars"], "invalid": ["<5_chars", ">100_chars"]}, "city": {"valid": ["2-50 letters and spaces"], "invalid": ["a", "city_name_over_50_chars_long_for_testing_purposes", "city_with_1_number"]}, "zip_postal_code": {"valid": ["valid_country_format"], "invalid": ["invalid_format"]}, "phone_number": {"valid": ["10-15 digits"], "invalid": ["<10_digits", ">15_digits"]}, "credit_card_number": {"valid": ["13-19_digits_luhn_valid"], "invalid": ["<13_digits", ">19_digits", "luhn_invalid_number"]}, "cvv": {"valid": ["3-4_digits"], "invalid": ["<3_digits", ">4_digits"]}, "expiry_date": {"valid": ["future_date_MM/YY"], "invalid": ["past_date_MM/YY", "invalid_date_format"]}, "button_action": {"valid": ["Place Order"], "invalid": []}}}, {"screen_name": "Customer Bookings Page", "variables": {}}, {"screen_name": "Booking Details Page", "variables": {"button_action": {"valid": ["Confirm Cancellation"], "invalid": []}}}, {"screen_name": "Create Booking Page", "variables": {"service_selection": {"valid": ["available_service"], "invalid": ["unavailable_service"]}, "date_time_selection": {"valid": ["available_slot"], "invalid": ["unavailable_slot", "past_date_time"]}, "special_instructions": {"valid": ["some_text", ""], "invalid": []}, "button_action": {"valid": ["Submit Booking"], "invalid": []}}}, {"screen_name": "User Profile Page", "variables": {}}, {"screen_name": "Edit Profile Page", "variables": {"shop_name": {"valid": ["3-50_alphanumeric_unique_name"], "invalid": ["s", "name_over_50_characters_long_for_testing_purposes", "duplicate_shop_name", "profane_name"]}, "shop_logo_banner_images": {"valid": ["jpeg_or_png_or_webp_under_5MB"], "invalid": ["unsupported_format.gif", ">5MB_file", "<200x200_pixels", ">2048x2048_pixels"]}, "business_name": {"valid": ["2-100_characters"], "invalid": ["a", ""]}, "business_description": {"valid": ["10-500_characters"], "invalid": ["<10_chars", ">500_chars"]}, "contact_phone": {"valid": ["valid_phone_format"], "invalid": ["invalid_phone_format"]}, "business_address": {"valid": ["complete_address"], "invalid": ["incomplete_address"]}, "button_action": {"valid": ["Save Changes"], "invalid": []}}}, {"screen_name": "Change Password Page", "variables": {"current_password": {"valid": ["correct_current_password"], "invalid": ["incorrect_current_password"]}, "new_password": {"valid": ["ValidNewPass1!"], "invalid": ["short", "no_uppercase_1!", "NO_LOWERCASE_1!", "NoSpecialChar1", "NoNumberChar!"]}, "confirm_new_password": {"valid": ["matches_new_password"], "invalid": ["does_not_match_new_password"]}, "button_action": {"valid": ["Change Password"], "invalid": []}}}, {"screen_name": "User Orders Page", "variables": {}}, {"screen_name": "Seller Dashboard", "variables": {}}, {"screen_name": "Seller Products Page", "variables": {"button_action": {"valid": ["Delete"], "invalid": []}}}, {"screen_name": "Edit Product Page", "variables": {"product_name": {"valid": ["5-100_chars_no_special"], "invalid": ["<5_chars", ">100_chars", "name_with_@"]}, "description": {"valid": ["20-2000_chars"], "invalid": ["<20_chars", ">2000_chars"]}, "price": {"valid": ["0.01-99999.99"], "invalid": ["0", "<0.01", ">99999.99"]}, "sku": {"valid": ["3-50_alphanumeric_unique_per_seller"], "invalid": ["<3_chars", ">50_chars", "duplicate_sku", "sku_with_!"]}, "images": {"valid": ["<=10_images_valid_format_size"], "invalid": [">10_images", "invalid_format_or_size"]}, "stock_quantity": {"valid": ["0-9999"], "invalid": ["-1", ">9999"]}, "button_action": {"valid": ["Save", "Update"], "invalid": []}}}, {"screen_name": "Seller Orders Page", "variables": {}}, {"screen_name": "Seller Order Details Page", "variables": {"order_status": {"valid": ["Processing", "Shipped", "Delivered"], "invalid": ["invalid_transition (e.g. Pending->Shipped)"]}, "tracking_information": {"valid": ["8-50_alphanumeric"], "invalid": ["<8_chars", ">50_chars"]}, "carrier": {"valid": ["UPS", "FedEx", "USPS", "DHL"], "invalid": ["not_in_list"]}, "button_action": {"valid": ["Save Changes"], "invalid": []}}}, {"screen_name": "Se<PERSON> Bookings Page", "variables": {}}, {"screen_name": "Seller Booking Details Page", "variables": {"booking_status": {"valid": ["Confirmed", "Completed", "Cancelled"], "invalid": ["invalid_status_transition"]}, "button_action": {"valid": ["Update Status"], "invalid": []}}}, {"screen_name": "Admin Dashboard", "variables": {}}, {"screen_name": "Admin Products Page", "variables": {"product_filter": {"valid": ["valid_filter_criteria"], "invalid": []}, "button_action": {"valid": ["Filter", "Delete"], "invalid": []}}}, {"screen_name": "Admin Pending Products Page", "variables": {"rejection_reason": {"valid": ["text_reason"], "invalid": []}, "button_action": {"valid": ["Approve", "Reject"], "invalid": []}}}, {"screen_name": "Admin Categories Page", "variables": {"category_name": {"valid": ["new_category_name"], "invalid": ["duplicate_category_name"]}, "button_action": {"valid": ["Create", "Edit", "Delete"], "invalid": []}}}, {"screen_name": "Admin Users Page", "variables": {"user_search": {"valid": ["existing_user_email"], "invalid": []}, "button_action": {"valid": ["Search", "Manage"], "invalid": []}}}, {"screen_name": "Admin Orders Page", "variables": {"order_filter": {"valid": ["valid_filter_criteria"], "invalid": []}, "button_action": {"valid": ["Filter"], "invalid": []}}}, {"screen_name": "Admin Order Details Page", "variables": {}}, {"screen_name": "Admin Sale Codes Page", "variables": {"sale_code": {"valid": ["new_unique_code"], "invalid": ["duplicate_code"]}, "discount": {"valid": ["percentage_value", "fixed_amount"], "invalid": ["invalid_value"]}, "expiry_date": {"valid": ["future_date"], "invalid": ["past_date"]}, "button_action": {"valid": ["Create", "Edit", "Delete"], "invalid": []}}}, {"screen_name": "Access Denied Page", "variables": {}}, {"screen_name": "Error <PERSON>", "variables": {}}], "navigation_rules": {"(Home Page, Login Page)": "User Account <PERSON>u", "(Home Page, Customer Registration Page)": "Sign Up link", "(Home Page, Product Detail Page)": "Product Cards", "(Home Page, Search Results Page)": "Search Bar", "(Home Page, Shopping Cart Page)": "Shopping Cart Icon", "(Home Page, About Us Page)": "Main Navigation", "(Home Page, Contact Page)": "Main Navigation", "(Home Page, Collaborations Page)": "Main Navigation", "(Login Page, Home Page)": "<PERSON><PERSON>", "(Login Page, Seller Dashboard)": "<PERSON><PERSON>", "(Login Page, Admin Dashboard)": "<PERSON><PERSON>", "(Login Page, Customer Registration Page)": "Registration Links", "(Login Page, Seller Registration Page)": "Registration Links", "(Customer Registration Page, Login Page)": "Register <PERSON>", "(Seller Registration Page, Login Page)": "Register <PERSON>", "(Search Results Page, Product Detail Page)": "Results Grid", "(Product Detail Page, Shopping Cart Page)": "Add to Cart Button", "(Product Detail Page, Home Page)": "Site Logo", "(Shopping Cart Page, User Orders Page)": "Checkout <PERSON><PERSON>", "(Shopping Cart Page, Home Page)": "Continue Shopping Button", "(User Profile Page, Edit Profile Page)": "Account <PERSON><PERSON>", "(User Profile Page, Change Password Page)": "Account <PERSON><PERSON>", "(User Profile Page, User Orders Page)": "Account <PERSON><PERSON>", "(User Profile Page, Customer Bookings Page)": "Account <PERSON><PERSON>", "(User Profile Page, Login Page)": "User Account <PERSON>u", "(Edit Profile Page, User Profile Page)": "Save Changes <PERSON>", "(Change Password Page, User Profile Page)": "Change Password <PERSON>", "(User Orders Page, Product Detail Page)": "Orders List", "(Customer Bookings Page, Booking Details Page)": "Booking Details Link", "(Customer Bookings Page, Create Booking Page)": "Create New Booking", "(Booking Details Page, Customer Bookings Page)": "Action Buttons", "(Create Booking Page, Customer Bookings Page)": "Submit Booking Button", "(Seller Dashboard, Seller Products Page)": "Navigation Menu", "(Seller Dashboard, Seller Orders Page)": "Navigation Menu", "(Seller Dashboard, Seller Bookings Page)": "Navigation Menu", "(Seller Dashboard, Edit Profile Page)": "Navigation Menu", "(Seller Dashboard, Login Page)": "User Account <PERSON>u", "(Seller Products Page, Edit Product Page)": "Add Product Button or Product Actions", "(Edit Product Page, Seller Products Page)": "Save/Update But<PERSON>", "(Seller Orders Page, Seller Order Details Page)": "Order Details Link", "(Seller Order Details Page, Seller Orders Page)": "Back to Orders", "(Seller Bookings Page, Seller Booking Details Page)": "Booking Details Link", "(Seller Booking Details Page, Seller Bookings Page)": "Back to Bookings", "(Admin Dashboard, Admin Products Page)": "Navigation Menu", "(Admin Dashboard, Admin Pending Products Page)": "Navigation Menu", "(Admin Dashboard, Admin Categories Page)": "Navigation Menu", "(Admin Dashboard, Admin Users Page)": "Navigation Menu", "(Admin Dashboard, Admin Orders Page)": "Navigation Menu", "(Admin Dashboard, Admin Sale Codes Page)": "Navigation Menu", "(Admin Dashboard, Login Page)": "User Account <PERSON>u", "(Admin Pending Products Page, Admin Products Page)": "Approval Actions", "(Admin Orders Page, Admin Order Details Page)": "Order Details Link", "(Admin Order Details Page, Admin Orders Page)": "Admin Actions", "(Error Page, Home Page)": "Home Page Link", "(Access Denied Page, Home Page)": "Navigation Options", "(Access Denied Page, Login Page)": "Login Suggestion"}}