1. Seller navigates to User Profile Page
2. Seller updates profile information in Edit Profile Page
3. <PERSON><PERSON> uploads images in Edit Profile Page
4. <PERSON>ller clicks "Save Changes" button in Edit Profile Page
5. Se<PERSON> navigates to Seller Products Page
6. Seller clicks "Add Product" button in Seller Products Page
7. Seller enters product information in Edit Product Page
8. Seller uploads images in Edit Product Page
9. <PERSON><PERSON> enters pricing details in Edit Product Page
10. <PERSON><PERSON> enters inventory details in Edit Product Page
11. Seller clicks "Save/Update Button" in Edit Product Page
12. Anonymous User navigates to Homepage
13. Anonymous User clicks "Sign Up" in Homepage
14. Anonymous User enters personal information in Customer Registration Page
15. Anonymous User enters password in Customer Registration Page
16. Anonymous User clicks "Register Button" in Customer Registration Page
17. User clicks "Login" in Homepage
18. User enters email in Login Page
19. User enters password in Login Page
20. User clicks "Login Button" in Login Page
21. Customer views product details in Product Detail Page
22. Customer selects options for product in Product Detail Page
23. Customer specifies quantity in Product Detail Page
24. Customer clicks "Add to Cart Button" in Product Detail Page
25. Customer clicks "Checkout Button" in Shopping Cart Page
26. Customer enters/confirms shipping information in Checkout Page
27. Customer selects payment method in Checkout Page
28. Customer reviews order details in Checkout Page
29. Customer clicks "Confirm Order" button in Checkout Page
30. Seller navigates to Seller Orders Page
31. Seller selects order to process in Seller Orders Page
32. Seller updates order status in Seller Order Details Page
33. Seller adds tracking information in Seller Order Details Page
34. Customer navigates to User Profile Page
35. Customer clicks 'Order History' link in User Profile Page
36. Customer views order details in User Orders Page
37. Customer navigates to Create Booking Page
38. Customer selects service in Create Booking Page
39. Customer selects date and time for booking in Create Booking Page
40. Customer enters special instructions in Create Booking Page
41. Customer clicks "Submit Booking Button" in Create Booking Page
42. Customer navigates to Customer Bookings Page
43. Customer selects booking to cancel in Customer Bookings Page
44. Customer confirms cancellation in Booking Details Page
45. Seller navigates to Seller Bookings Page
46. Seller selects booking to process in Seller Bookings Page
47. Seller processes cancellation in Seller Booking Details Page
48. Admin navigates to Admin Products Page
49. Admin filters products to view pending ones in Admin Products Page
50. Admin selects product to review in Admin Pending Products Page
51. Admin clicks "Approve" or "Reject" button in Admin Pending Products Page
52. Admin enters rejection reasons (if rejecting) in Admin Pending Products Page
Business Process Context: The interaction between a Customer and a Seller on the SASUCare platform represents a dynamic and continuous lifecycle, beginning long before a single purchase is made. Initially, a Seller establishes their digital presence by setting up a detailed shop pr ofile (UC -301) and meticulously populating their catalog with products, including images, descriptions, and accurate inventory levels (UC -302). A Customer then begins their journey by registering an account (UC -101) or logging in (UC -102), discovering the Seller's offerings, and adding a desired item to their shopping cart (UC -203). This culminates in the core transaction where the Customer completes the checkout process, providing payment and shipping details to finalize their order (UC -207). Immediately, the Seller is notified of the new sale and navigates their dashboard to manage the order (UC -305), updating its status from 'Processing' to 'Shipped' after dispatching the item and providing a tracking number, which the Customer can monitor through their o wn order history page (UC -208). Beyond this standard product flow, a different Customer might engage with the Seller's service offerings, viewing their availability and booking a specific appointment (UC -211). Should that Customer's plans change, they can then navigate back to their bookings to request a cancellation, which the Seller reviews and processes according to their stated policy (UC -212). Following these successful customer interactions, the Seller’s work continues as they might update their business information, refine product listings, or add entirely new items, which may require a brief period of pending approval before becoming visible to the public, e nsuring platform quality standards are met (reflecting UC -405). This entire ecosystem demonstrates a continuous loop of preparation, transaction, fulfillment, and ongoing management from both the Customer and Seller perspectives.
