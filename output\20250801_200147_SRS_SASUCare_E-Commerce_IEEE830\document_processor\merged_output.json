[{"heading_number": "1", "title": "1. Overall Description", "Content": ""}, {"heading_number": "1.1", "title": "1.1 Functional Requirements Design", "Content": "1.1.1"}, {"heading_number": "1.1.1", "title": "1.1.1 User Management Requirements", "Content": "Requirement ID  Requirement \nName  Description  Validation \nRules  \nFR-001  User \nRegistration  Allows \nanonymous \nusers to create \nnew accounts  Email \nuniqueness, \npassword \nstrength  \nFR-002  User \nAuthentication  Allows \nregistered \nusers to \nauthenticate \nand access their \ndashboard  Valid \ncredentials, \naccount \nverification  \nFR-003  Role \nManagement  Allows \nadministrators \nto assign and \nmanage user \nroles  Role \nassignment \nvalidation  \nFR-004  Profile \nManagement  Allows users to \nupdate their \npersonal profile \ninformation  Data validation, \nimage upload\n\nFR-005  Password Reset  Allows users to \nreset their \nforgotten \npasswords  Email \nverification, \ntoken \nvalidation  \nFR-006  User Account \nManagement  Allows \nadministrators \nto manage user \naccounts  Admin role \nvalidation  \nFR-007  User Session \nManagement  Manages user \nsessions and \nauthentication \nstate  Session \ntimeout, \nsecurity \nvalidation"}, {"heading_number": "1.1.2", "title": "1.1.2 Product Management Requirements", "Content": "Requirement ID  Requirement \nName  Description  Validation \nRules  \nFR-008  Product \nCreation  Allows sellers \nto create new \nproducts in \ntheir catalog  Required fields, \nprice validation  \nFR-009  Product Editing  Allows sellers \nto edit and \nupdate their \nexisting \nproducts  Owner \nvalidation, \nstatus check  \nFR-010  Product Search  Allows \ncustomers to \nsearch for \nproducts using \nvarious criteria  Search criteria \nvalidation  \nFR-011  Product Display  Display product \ninformation to \nusers  Data formatting \nvalidation  \nFR-012  Category \nManagement  Allows \nadministrators \nto create and \norganize \nproduct \ncategories  Admin role \nvalidation  \nFR-013  Image \nManagement  Allows sellers \nto upload and \nmanage \nproduct images  File type, size \nvalidation  \nFR-014  Product \nApproval  Allows \nadministrators \nto approve \npending \nproducts  Admin \napproval \nworkflow\n\nFR-015  Product \nInventory  Manage \nproduct stock \nand availability  Stock level \nvalidation"}, {"heading_number": "1.1.3", "title": "1.1.3 Shopping and Order Requirements", "Content": "Requirement ID  Requirement \nName  Description  Validation \nRules  \nFR-016  Shopping Cart  Allows \ncustomers to \nadd products to \ncart and \nmanage \ncontents  Stock \navailability, \nquantity limits  \nFR-017  Checkout \nProcess  Allows \ncustomers to \ncomplete \npurchase and \nfinalize \ntransactions  Address \nvalidation, \npayment info  \nFR-018  Order \nManagement  Allows sellers \nto view and \nprocess \ncustomer \norders  Order status \nvalidation  \nFR-019  Order History  Allows \ncustomers to \nview their \ncomplete order \nhistory  User ownership \nvalidation  \nFR-020  Address \nManagement  Allows users to \nmanage their \nshipping \naddresses  Address format \nvalidation  \nFR-021  Order Tracking  Allows \ncustomers to \ntrack order \nstatus  Order status \nupdates  \nFR-022  Order \nAdministration  Allows \nadministrators \nto oversee all \norders  Admin role \nvalidation  \nFR-023  Payment \nProcessing  Handle \npayment \ntransactions  Payment \nvalidation, \nsecurity"}, {"heading_number": "1.1.4", "title": "1.1.4 Booking System Requirements", "Content": "Requirement ID  Requirement \nName  Description  Validation \nRules\n\nFR-024  Booking \nCreation  Allows \ncustomers to \ncreate service \nbookings  Service \navailability, \ntime validation  \nFR-025  Booking \nManagement  Allows sellers \nto manage their \nservice \nbookings  Seller \nownership \nvalidation  \nFR-026  Booking Status \nUpdate  Allows service \nproviders to \nupdate booking \nstatus  Status \ntransition \nvalidation  \nFR-027  Booking \nHistory  Allows \ncustomers to \nview their \nbooking history  User ownership \nvalidation  \nFR-028  Booking \nCalendar  Display booking \nschedules and \navailability  Date/time \nvalidation  \nFR-029  Booking \nNotifications  Send \nnotifications for \nbooking \nu"}, {"heading_number": "1.1.5", "title": "1.1.5 Content Management Requirements", "Content": "1.1.5 Content  Management Requirements  \nRequirement ID  Requirement \nName  Description  Validation \nRules  \nFR-030  Company \nInformation \nDisplay  Display \ncompany \ninformation \nand policies  Content \nvalidation  \nFR-031  Contact \nInformation \nManagement  Manage and \ndisplay contact \ninformation  Contact form \nvalidation  \nFR-032  Partnership \nInformation  Display \ncollaboration \nand \npartnership \ninformation  Content \nmanagement \nvalidation  \nFR-033  Site Navigation  Provide \nconsistent \nnavigation \nacross the \nplatform  Navigation \nconsistency  \nFR-034  Search \nFunctionality  Global search \nacross products \nand content  Search query \nvalidation"}, {"heading_number": "1.1.6", "title": "1.1.6 Administrative Requirements", "Content": "Requirement ID  Requirement \nName  Description  Validation \nRules  \nFR-035  Platform \nDashboard  Provide \nadministrative \noverview of \nplatform  Admin role \nvalidation  \nFR-036  System \nMonitoring  Monitor system \nperformance \nand activities  System metrics \nvalidation  \nFR-037  Sale Code \nManagement  Create and \nmanage \npromotional \ncodes  Code validation, \nexpiry rules  \nFR-038  Business \nAnalytics  Provide \nbusiness \nintelligence and \nreporting  Data accuracy \nvalidation  \nFR-039  System \nConfiguration  Configure \nsystem settings \nand parameters  Configuration \nvalidation  \nFR-040  Data Export  Export system \ndata for \nreporting  Export format \nvalidation  \nFR-041  Audit Trail  Track system \nchanges and \nuser activities  Audit log \nvalidation"}, {"heading_number": "1.1.7", "title": "1.1.7 System and Security Requirements", "Content": "Requirement ID  Requirement \nName  Description  Validation \nRules  \nFR-042  Access Control  Enforce role -\nbased access to \nsystem \nfunctions  Permission \nvalidation  \nFR-043  Error Handling  Handle system \nerrors \ngracefully  Error message \nvalidation  \nFR-044  Data Security  Protect \nsensitive user \nand business \ndata  Encryption, \ndata protection  \nFR-045  Session \nSecurity  Secure user \nsessions and \nprevent \nunauthorized \naccess  Session \nvalidation  \nFR-046  Input \nValidation  Validate all user \ninputs for Input \nsanitization\n\nsecurity and \ndata integrity  \nFR-047  System Backup  Backup system \ndata and \nconfigurations  Backup \nvalidation"}, {"heading_number": "1.2", "title": "1.2 User Roles and Permissions", "Content": "User Role  Primary Functions  Access Level  \nAnonymous User  FR-010, FR -011, FR -030, \nFR-031, FR -032, FR -033, \nFR-034  Public access only  \nCustomer  FR-001, FR -002, FR -004, \nFR-005, FR -016, FR -017, \nFR-019, FR -020, FR -021, \nFR-024, FR -027  Authenticated user access  \nSeller  All Customer functions + \nFR-008, FR -009, FR -013, \nFR-015, FR -018, FR -025, \nFR-026, FR -028, FR -038  Seller -specific access + \nCustomer access  \nAdministrator  All system functions + FR -\n003, FR -006, FR -012, FR -\n014, FR -022, FR -035, FR -\n036, FR -037, FR -039, FR -\n040, FR -041 Full system access"}, {"heading_number": "1.3", "title": "1.3 Use Case Design", "Content": ""}, {"heading_number": "1.3.1", "title": "1.3.1 Register", "Content": "1.3.1 Regist er \nTable 1: Description of User Register (UC -101)  \nUC ID and Name  UC-101 - Register  \nPrimary Actor  Anonymous User  \nTrigger:  This requirement allows anonymous users \nto create new accounts on the platform by \nproviding personal information, selecting \ntheir role (Customer or Seller), and \ncompleting email verification to access \nsystem features.  \nDescription:  This use case allows an Anonymous User to \nregister for a new account on the SASUCare \nplatform by providing necessary details \nsuch as email, password, first name, last \nname, and optionally a shop name if \nregistering as a Seller. The system validates \nthe inp ut, creates a user record.  \nPreconditions:  The Anonymous User is not logged in.  \nPostconditions  A new user record is stored in the system \ndatabase. The user is redirected to the \nLoginPage upon successful registration.\n\nExpected results  Upon submitting valid registration details \nand confirming the email verification, the \nsystem saves the new user record. The user \ncan then login and access their role -specific \ndashboard.  \nNormal Flow:  1. Anonymous User navigates to the \nHomepage and clicks \"Sign Up\".  \n2. The system displays the \nRegistrationPage with a form.  \n3. Anonymous User enters email, password, \nconfirms password, first name, last name, \nand optionally shop name, then clicks \n\"Register\".  \n4. The system displays a success message \nand redirects to the LoginPage.  \nAlternative Flows:  Invalid Data  \n \n1. Anonymous User submits the form with \nmissing or incorrect data (e.g., duplicate \nemail, invalid password).  \n2. The system displays an error message \nnext to the relevant field.  \n3. Anonymous User corrects the data and \nresubmits.  \nExceptions:   \n Business Rules Applied:  \n • BR -001: Email uniqueness validation  \n  - Email must be unique across all user accounts  \n  - Email format must follow RFC 5322 standard (e.g., <EMAIL>)  \n  - Email domain must have valid MX record  \n • BR -002: Password strength requirements  \n  - Minimum 8 characters, maximum 128 characters  \n  - Must contain at least 1 uppercase letter (A -Z) \n  - Must contain at least 1 lowercase letter (a -z) \n  - Must contain at least 1 number (0 -9) \n  - Must contain at least 1 special character (!@#$%^&*)  \n  - Cannot contain user's email or name  \n • BR -003: Email verification requirement  \n  - Verification email must be sent within 30 seconds of registration  \n  - Verification token expires after 24 hours  \n  - Maximum 3 verification email resend attempts per day  \n • BR -004: Terms acceptance requirement  \n  - Terms and Conditions checkbox must be checked (required)\n\n- Privacy Policy acknowledgment required  \n  - Marketing consent is optional"}, {"heading_number": "1.3.2", "title": "1.3.2 <PERSON><PERSON>", "Content": "Table 2: User Login use case description UC -102 \nUC ID and Name  UC-102  - Login  \nPrimary Actor  Registered User (Seller or Customer)              \nTrigger:  This requirement allows registered users \nto authenticate themselves using email and \npassword credentials to access their \npersonalized dashboard and account -\nspecific features based on their role \n(Customer, Seller, or Admin).  \nDescription:  This use case allows a Registered User to \nauthenticate and access the SASUCare \nplatform by providing their email and \npassword. The system validates the \ncredentials, creates a user session, and \nredirects the user to their role -specific \ndashboard (Customer, Seller, or <PERSON>min).  \nPreconditions:  The user has a verified account with valid \ncredentials. The user is not currently \nlogged in.  \nPostconditions  User session is created and authenticated. \nUser is redirected to role -specific \ndashboard.  \nExpected results  Upon entering valid credentials, the system \nauthenticates the user and redirects them \nto their appropriate dashboard based on \ntheir role (Customer, Seller, or Admin).  \nNormal Flow:  1. Registered User navigates to the \nHomepage and clicks \"Login\".  \n2. The system displays the LoginPage with \nemail and password fields.  \n3. User enters email and password, then \nclicks \"Login\".  \n4. The system validates credentials and \nredirects to role -specific dashboard.  \nAlternative Flows:  Invalid Credentials  \n \n1. User enters incorrect email or password.  \n2. System displays error message \"Invalid \nemail or password\".  \n3. User corrects credentials and resubmits.  \n \nAccount Not Verified  \n \n1. User attempts to login with unverified\n\naccount.  \n2. System displays verification message.  \n3. System offers to resend verification \nemail.  \nExceptions:  Account Suspended: System detects \nsuspended account and displays \nsuspension message with contact \ninformation.  \n \n Business Rules Applied:  \n • BR -005: Account verification requirement  \n  - Account must be email verified before login  \n  - Unverified accounts blocked after 7 days of registration  \n  - Account verification status checked on each login attempt  \n • BR -006: Login attempt limitations  \n  - Maximum 5 failed login attempts per account per hour  \n  - Account locked for 30 minutes after 5 failed attempts  \n  - CAPTCHA required after 3 failed attempts  \n  - IP-based rate limiting: 20 attempts per IP per hour  \n • BR -007: Session timeout configuration  \n  - Session expires after 30 minutes of inactivity  \n  - \"Remember me\" extends session to 30 days  \n  - Session automatically renewed on user activity  \n  - Maximum concurrent sessions per user: 3  \n • BR -008: Role -based redirection  \n  - Customer role: Redirect to homepage or last visited product page  \n  - Seller role: Redirect to seller dashboard  \n  - Admin role: Redirect to admin panel  \n  - Redirect URL stored in session for post -login navigation"}, {"heading_number": "1.3.3", "title": "1.3.3 Add Product to Cart", "Content": "Table 3: Add Product to Cart use case description UC -203 \nUC ID and Name:  UC-203  - Add Product to Cart  \nPrimary Actor:  Customer  \nTrigger:  This requirement allows customers to add \nselected products to their shopping cart by \nspecifying quantity and product options, \nenabling them to collect items for future \npurchase while browsing the marketplace.  \nDescription:  This use case allows Customer to add a \nselected product to their shopping cart. The \nsystem validates product availability,\n\nchecks stock quantity, and updates the cart \nwith the selected item and quantity.  \nPreconditions:  Customer is viewing a product detail page. \nProduct is active and available.  \nPostconditions  Product is added to shopping cart. Cart \ntotals are updated. Cart item count is \nincremented.  \nExpected results  The selected product with specified \nquantity is successfully added to the cart. \nSystem displays confirmation message and \nupdates cart icon with new item count.  \nNormal Flow:  1. Customer views product detail page and \nselects options (size, color, etc.).  \n2. Customer specifies quantity and clicks \n\"Add to Cart\".  \n3. System validates product availability and \nstock quantity.  \n4. System adds item to cart and displays \nconfirmation message.  \nAlternative Flows:  Insufficient Stock  \n \n1. Customer selects quantity exceeding \navailable stock.  \n2. System displays stock limitation \nmessage.  \n3. System suggests maximum available \nquantity.  \n \nProduct Unavailable  \n \n1. Customer attempts to add inactive \nproduct.  \n2. System displays product unavailability \nmessage.  \nExceptions:  Invalid Quantity: Customer enters invalid \nquantity (0 or negative). System displays \nvalidation error.  \n \n Business Rules Applied:  \n • BR -009: Stock availability validation  \n  - Real -time stock check before adding to cart  \n  - Stock quantity must be > 0 for active products  \n  - Reserved stock (in other carts) considered unavailable  \n  - Stock validation timeout: 5 seconds maximum  \n • BR -010: Maximum quantity per item limits  \n  - Maximum 99 units per product per cart\n\n- Quantity must be positive integer (1 -99) \n  - Cannot exceed available stock quantity  \n  - Bulk purchase (>50 units) requires seller approval  \n • BR -011: Cart session persistence  \n  - Guest cart expires after 24 hours of inactivity  \n  - Registered user cart persists for 30 days  \n  - Cart automatically saved every 30 seconds  \n  - Maximum 50 different products per cart  \n • BR -012: Price consistency validation  \n  - Price locked when item added to cart for 2 hours  \n  - Price changes >10% require user confirmation  \n  - Currency must match user's selected currency  \n  - All prices displayed with 2 decimal places"}, {"heading_number": "1.3.4", "title": "1.3.4 View Shopping Cart", "Content": "Table 4: View Shopping Cart use case description UC -204 \nUC ID and Name  UC-204  - View Shopping Cart  \nPrimary Actor  Customer  \nTrigger:  This requirement allows customers to view \nall items currently in their shopping cart, \nmodify quantities, remove unwanted items, \nand review total costs before proceeding to \ncheckout.  \nDescription:  This use case allows Customer to view all \nitems currently in their shopping cart, \nincluding product details, quantities, prices, \nand total cost. Customer can also update \nquantities or remove items.  \nPreconditions:  Customer has items in cart or accessing \nempty cart.  \nPostconditions  Cart contents are displayed with current \ntotals and available actions.  \nExpected results  Customer can see all cart items with \ndetails, update quantities, remove items, \nand proceed to checkout if cart has items.  \nNormal Flow:  1. Customer clicks on cart icon or navigates \nto cart page.  \n2. System retrieves cart contents from \nsession.  \n3. System displays cart items with details \nand totals.  \n4. Customer can update quantities, remove \nitems, or proceed to checkout.  \nAlternative Flows:  Empty Cart\n\n1. Customer accesses cart with no items.  \n2. System displays empty cart message.  \n3. System provides 'Continue Shopping' \nlink.  \n \nPrice Changes  \n \n1. System detects price changes since items \nwere added.  \n2. System displays price change \nnotification.  \n3. System updates cart with new prices.  \nExceptions:  Cart synchronization issues: System \nhandles concurrent cart modifications with \noptimistic locking.  \n \n Business Rules Applied:  \n • BR -013: Cart session management  \n  - Cart data refreshed every page load  \n  - Session cart merged with database cart on login  \n  - Cart synchronization timeout: 10 seconds maximum  \n  - Concurrent cart modifications handled with optimistic locking  \n • BR -014: Price update notifications  \n  - Price changes displayed with old vs new price comparison  \n  - Notification shown for changes >5% or >$1.00  \n  - User must acknowledge price changes before checkout  \n  - Price change history maintained for 90 days  \n • BR -015: Cart expiration policies  \n  - Guest cart: 24 hours from last activity  \n  - Registered user cart: 30 days from last activity  \n  - Expired items automatically removed from cart  \n  - Email reminder sent 2 hours before cart expiration  \n • BR -016: Guest vs. registered user cart handling  \n  - Guest cart limited to 10 items maximum  \n  - Registered user cart allows up to 50 items  \n  - Guest cart requires email for checkout  \n  - Cart migration on registration preserves all items"}, {"heading_number": "1.3.5", "title": "1.3.5 Booking and Place Order", "Content": "Table 5: Booking and Place Order use case description UC -207  \nUC ID and Name  UC-207  - Booking and Place Order\n\nPrimary Actor  Customer                                     \nTrigger:  This requirement allows customers to \ncomplete their purchase by providing \nshipping information, selecting payment \nmethods, and confirming orders to finalize \ntransactions and receive products or \nservices.  \nDescription:  This use case allows Customer to complete \ntheir purchase by providing shipping \ninformation, selecting payment method, \nand confirming the order. The system \nprocesses the order and sends \nconfirmation.  \nPreconditions:  Customer has items in cart and is logged in.   \nPostconditions  Order is created, inventory is reserved, and \nconfirmation email is sent.  \nExpected results  Order is successfully placed with order \nnumber generated. Customer receives \nconfirmation email and is redirected to \norder confirmation page.  \nNormal Flow:  1. Customer navigates to checkout page \nfrom cart.  \n2. Customer enters/confirms shipping \ninformation.  \n3. Customer selects payment method and \nreviews order.  \n4. Customer confirms order placement.  \n5. System creates order and sends \nconfirmation.  \nAlternative Flows:  Invalid Shipping Address  \n \n1. Customer enters invalid address.  \n2. System displays validation errors.  \n3. Customer corrects address information.  \n \nPayment Processing Failed  \n \n1. Payment gateway returns error.  \n2. System displays payment error message.  \n3. System releases reserved inventory.  \nExceptions:  Insufficient Inventory: System detects \ninsufficient stock during checkout and \nupdates cart with available quantities.  \n \n Business Rules Applied:  \n • BR -017: Address validation requirements  \n  - Street address: 5 -100 characters, alphanumeric and spaces\n\n- City: 2 -50 characters, letters and spaces only  \n  - ZIP/Postal code: Format validated by country (e.g., 12345 or 12345 -6789 for US)  \n  - Phone number: 10 -15 digits, international format supported  \n • BR -018: Inventory reservation during checkout  \n  - Items reserved for 15 minutes during checkout process  \n  - Reservation automatically released if checkout not completed  \n  - Maximum 3 concurrent reservations per user  \n  - Reservation extends by 5 minutes on each checkout step completion  \n • BR -019: Payment processing validation  \n  - Credit card number: 13 -19 digits, Luhn algorithm validation  \n  - CVV: 3 -4 digits depending on card type  \n  - Expiry date: Must be future date, MM/YY format  \n  - Payment processing timeout: 30 seconds maximum  \n • BR -020: Order confirmation email requirement  \n  - Confirmation email sent within 60 seconds of order placement  \n  - Email includes order number, items, total, and delivery estimate  \n  - Email delivery retry: 3 attempts with exponential backoff  \n  - Backup SMS notification if email fails after 3 attempts"}, {"heading_number": "1.3.6", "title": "1.3.6 View Order History", "Content": "Table 6: View Order History use case description UC -208 \nUC ID and Name  UC-208  - View Order History  \nPrimary Actor  Customer                                     \nTrigger:  This requirement allows customers to view \ntheir complete order history with current \nstatus information, track shipments, access \norder details, and reorder previous \npurchases for convenience.  \nDescription:  This use case allows Customer to view all \ntheir past orders with current status \ninformation, order details, tracking \ninformation, and the ability to reorder or \nreview products.  \nPreconditions:  Customer is logged in and has placed \norders previously.   \nPostconditions  Order history is displayed with current \nstatus and available actions.  \nExpected results  Customer can view complete order history \nwith status updates, track shipments, and \naccess order details for reference or \nreordering.  \nNormal Flow:  1. Customer navigates to 'My Account' \nsection.\n\n2. Customer clicks on 'Order History'.  \n3. System retrieves customer's order \nhistory.  \n4. System displays orders in reverse \nchronological order.  \n5. Customer can view order details by \nclicking on specific orders.  \nAlternative Flows:  No Orders Found  \n \n1. System finds no orders for customer.  \n2. System displays 'No orders found' \nmessage.  \n3. System provides link to continue \nshopping.  \n \nOrder Details Unavailable  \n \n1. System cannot retrieve order details.  \n2. System displays error message.  \n3. System provides customer support \ncontact.  \nExceptions:  Data synchronization issues: System \nhandles order status updates with eventual \nconsistency model.  \n \n Business Rules Applied:  \n • BR -021: Order history access restrictions  \n  - Users can only view their own orders  \n  - Order history limited to last 2 years for performance  \n  - Guest orders accessible via email + order number for 90 days  \n  - <PERSON>min can view all orders with audit logging  \n • BR -022: Order status display requirements  \n  - Status updates in real -time with 5 -second refresh interval  \n  - Status history shows timestamps with timezone  \n  - Estimated delivery date updated based on current status  \n  - Status change notifications sent within 2 minutes  \n • BR -023: Order detail privacy protection  \n  - Sensitive data (payment info) masked in order history  \n  - Full order details require password re -authentication  \n  - Order sharing limited to order number and basic status  \n  - Personal information encrypted in database storage"}, {"heading_number": "1.3.7", "title": "1.3.7 Manage Seller Profile/Shop Information", "Content": "Table 7: Manage Seller Profile use case description UC -301\n\nUC ID and Name  UC-301  - Manage Seller Profile/Shop \nInformation  \nPrimary Actor  Seller                                     \nTrigger:  This requirement allows sellers to update \ntheir profile information and shop details \nincluding business name, description, \ncontact information, and branding images \nto present their business professionally to \ncustomers.  \nDescription:  This use case allows a Seller to update their \nprofile information and shop details \nincluding shop name, description, contact \ninformation, and shop logo/banner images.  \nPreconditions:  Seller is logged in with active account.  \nPostconditions  Profile information is updated and saved in \nthe system.  \nExpected results  Seller profile and shop information are \nsuccessfully updated. Changes are reflected \nacross the platform.  \nNormal Flow:  1. <PERSON><PERSON> navigates to shop settings or \nprofile section.  \n2. System displays current profile \ninformation.  \n3. Seller updates desired information and \nuploads images.  \n4. Seller saves changes and system \nvalidates information.  \n5. System displays success confirmation.  \nAlternative Flows:  Invalid Shop Name  \n \n1. Seller enters invalid or duplicate shop \nname.  \n2. System displays validation error.  \n3. <PERSON><PERSON> corrects shop name.  \n \nImage Upload Failed  \n \n1. System fails to process uploaded image.  \n2. System displays upload error message.  \n3. <PERSON><PERSON> can retry or skip image upload.  \nExceptions:  Required Fields Missing: System detects \nmissing required fields and highlights them \nfor correction.  \n \n Business Rules Applied:  \n • BR -024: Shop name uniqueness validation  \n  - Shop name: 3 -50 characters, alphanumeric and spaces\n\n- Must be unique across all sellers (case -insensitive)  \n  - Cannot contain profanity or restricted words  \n  - Name change allowed once every 30 days  \n • BR -025: Image file size and format restrictions  \n  - Supported formats: JPEG, PNG, WebP only  \n  - Maximum file size: 5MB per image  \n  - Minimum dimensions: 200x200 pixels  \n  - Maximum dimensions: 2048x2048 pixels  \n  - Images automatically compressed to <1MB for web display  \n • BR -026: Required profile information validation  \n  - Business name: 2 -100 characters (required for sellers)  \n  - Business description: 10 -500 characters  \n  - Contact phone: Valid phone number format  \n  - Business address: Complete address required for tax purposes  \n • BR -027: Shop information approval process  \n  - Profile changes reviewed within 24 hours  \n  - Major changes (business name, category) require admin approval  \n  - Seller notified via email within 2 hours of approval/rejection  \n  - Rejected changes include specific reason and improvement suggestions"}, {"heading_number": "1.3.8", "title": "1.3.8 Manage Products", "Content": "Table 8: Manage Products use case description UC -302  \nUC ID and Name  UC-302  - Manage Products  \nPrimary Actor  Seller                                     \nTrigger:  This requirement allows sellers to perform \ncomplete product management including \ncreating new products, editing existing \nones, uploading images, setting prices, \nmanaging inventory, and removing \nproducts from their catalog.  \nDescription:  This use case allows a Seller to perform full \nCRUD operations on their products \nincluding creating new products, editing \nexisting ones, uploading images, setting \nprices, and managing inventory.  \nPreconditions:  Seller is logged in with active account.  \nPostconditions  Product is created/updated/deleted in the \nsystem database.  \nExpected results  Product operations are completed \nsuccessfully. Product information is saved \nand reflected in the marketplace.  \nNormal Flow:  1. <PERSON><PERSON> navigates to product management \npage.\n\n2. <PERSON><PERSON> selects action (Add/Edit/Delete \nproduct).  \n3. For Add/Edit: Seller enters product \ninformation and uploads images.  \n4. Seller sets pricing and inventory details.  \n5. System validates and saves product \nrecord.  \nAlternative Flows:  Invalid Product Data  \n \n1. <PERSON><PERSON> submits form with invalid or \nmissing data.  \n2. System displays validation errors.  \n3. <PERSON>ller corrects data and resubmits.  \n \nImage Processing Failed  \n \n1. System fails to process uploaded images.  \n2. System displays image error message.  \n3. Seller can retry image upload.  \nExceptions:  Delete Confirmation: For delete action, \nsystem displays confirmation dialog before \npermanent removal.  \n \n Business Rules Applied:  \n • BR -028: Product information completeness validation  \n  - Product name: 5 -100 characters, no special characters except hyphens  \n  - Description: 20 -2000 characters, rich text supported  \n  - Price: $0.01 - $99,999.99, two decimal places  \n  - SKU: 3 -50 characters, alphanumeric, must be unique per seller  \n • BR -029: Image format and size restrictions  \n  - Maximum 10 images per product  \n  - First image becomes primary product image  \n  - Same format restrictions as BR -025  \n  - Image order can be changed via drag -and-drop interface  \n • BR -030: Pricing validation rules  \n  - Price changes >20% require admin approval  \n  - Sale price cannot be higher than regular price  \n  - Bulk pricing tiers: 10+, 50+, 100+ units with minimum 5% discount  \n  - Price history maintained for 1 year for analytics  \n • BR -031: Inventory management requirements  \n  - Stock quantity: 0 -9999 units  \n  - Low stock alert when quantity ≤ 5 units\n\n- Out of stock products automatically hidden from search  \n  - Inventory updates reflected in real -time across all sessions"}, {"heading_number": "1.3.9", "title": "1.3.9 Manage Orders Received", "Content": "Table 9: Manage Orders Received use case description UC -305  \nUC ID and Name  UC-305  - Manage Orders Received  \nPrimary Actor  Seller                                     \nTrigger:  This requirement allows sellers to view \nand process customer orders containing \ntheir products, update order status, add \ntracking information, and communicate \nwith customers throughout the fulfillment \nprocess.  \nDescription:  This use case allows a Seller to view and \nprocess orders containing their products, \nupdate order status, add tracking \ninformation, and communicate with \ncustomers about order progress.  \nPreconditions:  Seller is logged in and has received orders \nfor their products.  \nPostconditions  Order status is updated and customer is \nnotified of changes.  \nExpected results  Orders are processed efficiently with \nproper status updates. Customers receive \ntimely notifications about their order \nprogress.  \nNormal Flow:  1. Seller navigates to order management \npage.  \n2. System displays list of orders for seller's \nproducts.  \n3. Seller selects order to process.  \n4. System displays order details and \ncurrent status.  \n5. Seller updates order status and add  \ntracking information.  \n6. System saves changes and sends \ncustomer notification.  \nAlternative Flows:  No Orders Found  \n \n1. System finds no orders for seller.  \n2. System displays 'No orders found' \nmessage.  \n \nInvalid Status Transition  \n \n1. Seller attempts invalid status change.  \n2. System displays validation error.  \n3. Seller corrects status selection.\n\nTracking Information Required  \n \n1. System requires tracking info for \nshipped status.  \n2. System prompts seller to enter tracking \ndetails.  \nExceptions:  Communication failure: System handles \nnotification delivery failures with retry \nmechanism and fallback options.  \n \n Business Rules Applied:  \n • BR -032: Order status transition validation  \n  - Valid transitions: Pending→Processing→Shipped→Delivered  \n  - Cannot skip status levels (e.g., Pending→Shipped)  \n  - Status change requires reason if reverting (e.g., Shipped→Processing)  \n  - Delivered status cannot be changed after 7 days  \n • BR -033: Customer notification requirements  \n  - Email notification sent within 5 minutes of status change  \n  - SMS notification for Shipped and Delivered status (if opted in)  \n  - Notification includes tracking number and estimated delivery  \n  - Customer can opt -out of non -critical notifications  \n • BR -034: Tracking information requirements  \n  - Tracking number: 8 -50 characters, alphanumeric  \n  - Carrier selection from predefined list (UPS, FedEx, USPS, DHL)  \n  - Tracking URL automatically generated based on carrier  \n  - Tracking info required within 24 hours of marking as Shipped  \n • BR -035: Seller order access restrictions  \n  - Sellers can only view orders containing their products  \n  - Order details filtered to show only seller's items  \n  - Customer personal information limited to shipping address only  \n  - Order access logged for audit purposes"}, {"heading_number": "1.3.10", "title": "1.3.10 Manage Categories", "Content": "Table 10: Manage Categories use case description UC -404 \nUC ID and Name  UC-404  - Manage Categories  \nPrimary Actor  Administrator                                     \nTrigger:  This requirement allows administrators to \ncreate, edit, and organize the hierarchical \nstructure of product categories used \nthroughout the platform, enabling proper\n\nproduct classification and navigation for \nusers.  \nDescription:  This use case allows an Administrator to \ncreate, edit, and organize the hierarchical \nstructure of product categories used \nthroughout the platform for product \nclassification.  \nPreconditions:  <PERSON>min is logged in with appropriate \npermissions.   \nPostconditions  Category structure is updated and reflected \nacross the platform.  \nExpected results  Category hierarchy is successfully \nmanaged. Changes are immediately visible \nto sellers and customers.  \nNormal Flow:  1. <PERSON><PERSON> navigates to category \nmanagement page.  \n2. System displays current category \nhierarchy.  \n3. Admin selects action (Add/Edit/Delete \ncategory).  \n4. Admin enters category information and \nsets properties.  \n5. System validates and updates category \nstructure.  \nAlternative Flows:  Duplicate Category Name  \n \n1. <PERSON><PERSON> enters duplicate category name.  \n2. System displays validation error.  \n3. Admin corrects category name.  \n \nCategory Has Products  \n \n1. Admin attempts to delete category with \nproducts.  \n2. System displays warning about existing \nproducts.  \n3. Admin must reassign products or \nconfirm deletion.  \nExceptions:  Invalid Parent Category: System detects \ncircular reference in hierarchy and displays \nerror message.  \n \n Business Rules Applied:  \n • BR -036: Category name uniqueness validation  \n  - Category name: 2 -50 characters, letters, numbers, spaces, hyphens  \n  - Must be unique within the same parent category level\n\n- Case -insensitive uniqueness check  \n  - Reserved words (Admin, System, Test) not allowed  \n • BR -037: Category hierarchy validation  \n  - Maximum 5 levels of category depth  \n  - Cannot create circular references (category as its own parent)  \n  - Parent category must be active to create subcategories  \n  - Category path must be unique (e.g., Electronics/Phones/iPhone)  \n • BR -038: Product reassignment requirements  \n  - Products must be reassigned before category deletion  \n  - Bulk reassignment tool available for >10 products  \n  - Reassignment requires confirmation from affected sellers  \n  - Default \"Uncategorized\" category available as fallback  \n • BR -039: Category deletion restrictions  \n  - Cannot delete categories with active products  \n  - Cannot delete categories with subcategories  \n  - Soft delete: Category marked inactive, not physically removed  \n  - Deletion requires admin confirmation with reason"}, {"heading_number": "1.3.11", "title": "1.3.11 Manage All Products", "Content": "Table 11: Manage All Products use case description UC -405  \nUC ID and Name  UC-405  - Manage All Products  \nPrimary Actor  Administrator                                     \nTrigger:  This requirement allows administrators to \noversee and manage all products on the \nplatform including approving, rejecting, \nediting, or removing products from any \nseller to maintain platform quality and \ncompliance standards.  \nDescription:  This use case allows an Administrator to \noversee and manage all products on the \nplatform including approving, rejecting, \nediting, or removing products from any \nseller.  \nPreconditions:  Admin is logged in with appropriate \npermissions.   \nPostconditions  Product oversight actions are completed \nand logged.  \nExpected results  Administrative actions on products are \nsuccessfully executed. Affected sellers are \nnotified of changes.  \nNormal Flow:  1. Admin navigates to product oversight \npage.  \n2. System displays all products with \nfiltering options.\n\n3. Admin searches/filters products as \nneeded.  \n4. Admin selects product and performs \naction.  \n5. System validates permissions and \nupdates product status.  \nAlternative Flows:  Product Not Found  \n \n1. Search/filter returns no results.  \n2. System displays 'No products found' \nmessage.  \n \nInsufficient Permissions  \n \n1. Admin lacks permission for selected \naction.  \n2. System displays access denied message.  \n \nProduct Has Active Orders  \n \n1. Admin attempts to delete product with \nactive orders.  \n2. System displays warning about active \norders.  \nExceptions:  System logs all administrative actions with \ntimestamp, admin ID, and reason for audit \npurposes.  \n \n Business Rules Applied:  \n • BR -040: Admin permission validation  \n  - Admin actions require role -based permissions (view, edit, delete)  \n  - Sensitive actions require secondary authentication (password/2FA)  \n  - Admin activity logged with timestamp, IP, and action details  \n  - Super admin role required for system -wide changes  \n • BR -041: Product action logging requirements  \n  - All product changes logged with before/after values  \n  - Log retention: 2 years for compliance  \n  - Logs include admin ID, timestamp, IP address, reason  \n  - Critical actions (delete, suspend) require approval workflow  \n • BR -042: Seller notification requirements  \n  - Seller notified within 1 hour of admin action on their products  \n  - Notification includes specific reason and next steps  \n  - Appeals process available for rejected/suspended products  \n  - Notification delivery confirmed via email receipt\n\n• BR -043: Product deletion restrictions  \n  - Cannot delete products with pending/processing orders  \n  - Cannot delete products with active bookings  \n  - Soft delete preferred: product marked inactive instead  \n  - Hard delete requires 30 -day waiting period after soft delete"}, {"heading_number": "1.3.12", "title": "1.3.12 View Booking History & Status", "Content": "Table 12: View Booking History use case description UC -211 \nUC ID and Name  UC-211  - View Booking History & Status  \nPrimary Actor  Customer                                     \nTrigger:  This requirement allows customers to view \ntheir complete service booking history with \nreal -time status information, manage \nupcoming appointments, and access \nbooking details for reference and planning \npurposes.  \nDescription:  This use case allows Customer to view all \ntheir past and current service bookings \nwith real -time status information, booking \ndetails, and the ability to manage upcoming \nbookings.  \nPreconditions:  Customer is logged in and has made service \nbookings previously.   \nPostconditions  Booking history is displayed with current \nstatus and available management options.  \nExpected results  Customer can view complete booking \nhistory with status updates, manage \nupcoming bookings, and access booking \ndetails for reference.  \nNormal Flow:  1. Customer navigates to 'My Account' \nsection.  \n2. Customer clicks on 'Booking History'.  \n3. System retrieves customer's booking \nhistory.  \n4. System displays bookings with status \ninformation.  \n5. Customer can view booking details by \nclicking on specific bookings.  \nAlternative Flows:  No Bookings Found  \n \n1. System finds no bookings for customer.  \n2. System displays 'No bookings found' \nmessage.  \n3. System provides link to browse services.  \n \nBooking Details Unavailable  \n \n1. System cannot retrieve booking details.\n\n2. System displays error message.  \n3. System provides customer support \ncontact.  \nExceptions:  Real -time status synchronization issues: \nSystem handles booking status updates \nwith eventual consistency and manual \nrefresh options.  \n \n Business Rules Applied:  \n • BR -044: Booking history access restrictions  \n  - Users can only view their own bookings  \n  - Booking history limited to last 3 years  \n  - Guest bookings accessible via email + booking reference for 180 days  \n  - Shared booking access requires explicit permission from booking owner  \n • BR -045: Booking status display requirements  \n  - Status updates in real -time with 10 -second refresh interval  \n  - Status history includes timestamps and responsible party  \n  - Booking progress indicator shows completion percentage  \n  - Automated status updates based on service date/time  \n • BR -046: Booking detail privacy protection  \n  - Personal information encrypted in database  \n  - Payment details masked in booking history  \n  - Service provider sees only necessary contact information  \n  - Data retention: 7 years for tax/legal compliance"}, {"heading_number": "1.3.13", "title": "1.3.13 Cancel Booking", "Content": "Table 13: Cancel Booking use case description UC -212  \nUC ID and Name  UC-212  - Cancel Booking  \nPrimary Actor  Customer                                     \nTrigger:  This requirement allows customers to \ncancel existing service bookings according \nto the cancellation policy, with automatic \nrefund processing and service provider \nnotification to manage their appointments \neffectively.  \nDescription:  This use case allows Customer to cancel an \nexisting service booking based on the \ncancellation policy, with automatic refund \nprocessing and service provider \nnotification.  \nPreconditions:  Customer has active booking that can be \ncancelled according to cancellation policy.\n\nPostconditions  Booking is cancelled, seller is notified, and \nrefund is processed according to policy.  \nExpected results  Booking is successfully cancelled with \nappropriate refund amount. Service \nprovider is immediately notified and \ncalendar is updated.  \nNormal Flow:  1. Customer accesses booking history and \nselects booking to cancel.  \n2. System checks if booking can be \ncancelled based on policy.  \n3. System displays cancellation \nconfirmation with refund amount.  \n4. Customer confirms cancellation.  \n5. System updates booking status and \nprocesses refund.  \n6. System sends notification to service \nprovider.  \nAlternative Flows:  Booking Cannot Be Cancelled  \n \n1. System determines booking is not \ncancellable.  \n2. System displays cancellation policy \nmessage.  \n3. System provides customer support \ncontact.  \n \nCancellation Deadline Passed  \n \n1. System detects cancellation deadline has \npassed.  \n2. System displays deadline message.  \n3. Customer can contact support for \nassistance.  \nExceptions:  Refund processing failure: System handles \npayment gateway errors with retry \nmechanism and manual processing \nfallback.  \n \n Business Rules Applied:  \n • BR -047: Booking cancellation policy validation  \n  - Free cancellation up to 24 hours before service date  \n  - 50% refund for cancellations 12 -24 hours before  \n  - No refund for cancellations <12 hours before service  \n  - Emergency cancellations reviewed case -by-case  \n • BR -048: Cancellation deadline enforcement  \n  - System automatically blocks cancellations after deadline\n\n- Deadline calculated based on service date/time and timezone  \n  - Grace period: 15 minutes for system processing delays  \n  - Override available for admin with documented reason  \n • BR -049: Seller notification requirements  \n  - Seller notified immediately via email and SMS (if enabled)  \n  - Notification includes cancellation reason and refund amount  \n  - Seller can dispute cancellation within 2 hours  \n  - Automated calendar update sent to seller's booking system  \n • BR -050: Refund processing rules  \n  - Refunds processed within 3 -5 business days  \n  - Refund amount calculated based on cancellation timing  \n  - Processing fee: $2.99 for refunds <$50, 5% for refunds ≥$50  \n  - Refund status tracking available in user account"}, {"heading_number": "2", "title": "2. External Interface Requirements", "Content": ""}, {"heading_number": "2.1", "title": "2.1 User Interface Design", "Content": ""}, {"heading_number": "2.1.1", "title": "2.1.1 Screen Design Specifications", "Content": ""}, {"heading_number": "2.1.1.1", "title": "2.1.1.1 Screen Access Summary", "Content": "Screen  Purpose  Access Level  \nHome  Page  Main landing page, browse \nfeatured products and \nnavigate to other sections  Public  \nProduct Detail Page  View detailed product \ninformation and \nspecifications  Public  \nSearch Results Page  Browse and search through \navailable products with \nfiltering options  Public  \nAbout Us Page  Display company \ninformation and mission  Public  \nContact Page  Provide contact information \nand communication form  Public  \nCollaborations Page  Information about \npartnerships and \ncollaboration opportunities  Public  \nLogin Page  Authenticate and access \nuser accounts  Public  \nCustomer Registration Page  Create customer accounts \non the platform  Public  \nSeller Registration Page  Create seller accounts for \nshop owners  Public\n\nShopping Cart Page  Review and manage items \nbefore checkout  Customer  \nCustomer Bookings Page  View and manage service \nbookings  Customer  \nBooking Details Page  View detailed information \nabout specific bookings  Customer  \nCreate Booking Page  Create new service \nbookings  Customer  \nUser Profile Page  Manage personal \ninformation and settings  Authenticated  \nEdit Profile Page  Update personal profile \ninformation  Authenticated  \nChange Password Page  Update account password  Authenticated  \nUser Orders Page  View past orders and track \ncurrent ones  Customer  \nSeller Dashboard  Monitor business \nperformance and activities  Seller  \nSeller Products Page  Create, edit, and manage \nseller product catalog  Seller  \nEdit Product Page  Edit and update existing \nproducts  Seller  \nSeller Orders Page  Process and fulfill customer \norders  Seller  \nSeller Order Details Page  View detailed information \nabout specific orders  Seller  \nSeller Bookings Page  Manage service bookings \nfor sellers  Seller  \nSeller Booking Details Page  View detailed information \nabout specific service \nbookings  Seller  \nAdmin Dashboard  Oversee and manage the \nentire platform  Admin  \nAdmin Products Page  Manage all products in the \nsystem  Admin  \nAdmin Pending Products \nPage  Review and approve \npending product \nsubmissions  Admin  \nAdmin Categories Page  Organize and maintain \nproduct categories  Admin  \nAdmin Users Page  Manage user accounts and \npermissions  Admin  \nAdmin Orders Page  Oversee all orders in the \nsystem  Admin  \nAdmin Order Details Page  View detailed information \nabout any order  Admin  \nAdmin Sale Codes Page  Manage promotional codes \nand discounts  Admin  \nAccess Denied Page  Handle unauthorized access \nattempts  System\n\nError  Page  Handle  errors of system  System"}, {"heading_number": "*******", "title": "******* Screen Categories", "Content": ""}, {"heading_number": "*******.1", "title": "*******.1 Public Access Screens", "Content": "These screens are accessible to all users without authentication:  \nScreen Category  Screen Name  Related \nRequirements  Primary Functions  \nPublic Access  Home Page  FR-010, FR -011, FR -\n033, FR -034 Product browsing, \nsearch, navigation  \nPublic Access  Product Detail Page  FR-011  Product information \ndisplay  \nPublic Access  Search Results Page  FR-010, FR -034  Product search and \nfiltering  \nPublic Access  About Us Page  FR-030  Company \ninformation display  \nPublic Access  Contact Page  FR-031  Customer \ncommunication  \nPublic Access  Collaborations Page  FR-032  Partnership \ninformation  \nPublic Access  Login Page  FR-002, FR -005  User authentication  \nPublic Access  Customer \nRegistration Page  FR-001  Customer account \ncreation  \nPublic Access  Seller Registration \nPage  FR-001  Seller account \ncreation"}, {"heading_number": "*******.2", "title": "*******.2 Customer Access Screens", "Content": "These screens require customer -level authentication:  \nScreen Category  Screen Name  Related \nRequirements  Primary Functions  \nCustomer Access  Shopping Cart Page  FR-016, FR -017  Cart management, \ncheckout  \nCustomer Access  Customer Bookings \nPage  FR-027  Service booking \noverview  \nCustomer Access  Booking Details \nPage  FR-027  Booking \ninformation display  \nCustomer Access  Create Booking Page  FR-024  New booking \ncreation  \nCustomer Access  User Orders Page  FR-019, FR -021  Order history and \ntracking"}, {"heading_number": "*******.3", "title": "*******.3 Seller Access Screens", "Content": "These screens require seller -level authentication:  \nScreen Category  Screen Name  Related \nRequirements  Primary Functions\n\nSeller Access  Seller Dashboard  FR-038  Business \nperformance \nmonitoring  \nSeller Access  Seller Products Page  FR-008, FR -015  Product catalog \nmanagement  \nSeller Access  Edit Product Page  FR-008, FR -009, FR -\n013  Product editing and \nimage management  \nSeller Access  Seller Orders Page  FR-018  Order processing  \nSeller Access  Seller Order Details \nPage  FR-018  Order detail \nmanagement  \nSeller Access  Seller Bookings \nPage  FR-025, FR -028  Service booking \nmanagement  \nSeller Access  Seller Booking \nDetails Page  FR-025, FR -026  Booking detail \nmanagement"}, {"heading_number": "*******.4", "title": "*******.4 Admin Access Screens", "Content": "These screens require Admin -level authentication:  \nScreen Category  Screen Name  Related \nRequirements  Primary Functions  \nAdmin Access  Admin Dashboard  FR-035, FR -036  Platform oversight  \nAdmin Access  Admin Products \nPage  FR-015, FR -039  System -wide \nproduct \nmanagement  \nAdmin Access  Admin Pending \nProducts Page  FR-014  Product approval \nworkflow  \nAdmin Access  Admin Categories \nPage  FR-012  Category \nmanagement  \nAdmin Access  Admin Users Page  FR-003, FR -006  User account \nmanagement  \nAdmin Access  Admin Orders Page  FR-022  System -wide order \nmanagement  \nAdmin Access  Admin Order Details \nPage  FR-022  Order detail \noversight  \nAdmin Access  Admin Sale Codes \nPage  FR-037  Promotional code \nmanagement"}, {"heading_number": "*******.5", "title": "*******.5 System Access Screens", "Content": "These screens belong  to System -level : \nScreen Category  Screen Name  Related \nRequirements  Primary Functions  \nSystem  Access Denied Page  FR-042, FR -043  Unauthorized access \nhandling  \nSystem  Error Page  FR-043  System error \nhandling"}, {"heading_number": "2.1.2", "title": "2.1.2 Common UI Components", "Content": ""}, {"heading_number": "*******", "title": "******* Header Component", "Content": "Element  Type  Description  Access  Level  \nSite Logo  Visual  Clickable logo linking \nto homepage  Public  \nMain Navigation  Menu  Primary navigation \nmenu (SHOP, \nCOLLABS, CONTACT, \nABOUT US)  Public  \nSearch Bar  Input  Global product search \nfunctionality  Public  \nUser Account Menu  Dropdown  User profile access, \nlogin/logout, account \nsettings  Authenticated  \nShopping Cart Icon  Action  Cart access with item \ncount indicator  Public"}, {"heading_number": "*******", "title": "******* Footer Component", "Content": "Element  Type  Description  \nCompany Information  Content  Company name, address, \nand contact details  \nQuick Links  Navigation  Links to important pages \n(About, Contact, Terms, \nPrivacy)  \nSocial Media Links  Navigation  Links to company social \nmedia profiles  \nNewsletter Signup  Form  Email subscription form for \nmarketing communications  \nCopyright Notice  Content  Copyright information and \nlegal disclaimers"}, {"heading_number": "*******", "title": "******* Form Components", "Content": "Component  Type  Usage  \nText Input Fields  Input  Standard text inputs with \nvalidation and error states  \nPassword Fields  Input  Secure password inputs \nwith show/hide toggle  \nDropdown Selects  Input  Category selection, country \nselection, status filters  \nCheckboxes  Input  Terms agreement, feature \ntoggles, bulk selections  \nRadio Buttons  Input  Single -choice selections \n(payment methods, \nshipping options)  \nFile Upload  Input  Image uploads for products \nand profiles\n\nSubmit Buttons  Action  Primary and secondary \naction buttons with loading \nstates"}, {"heading_number": "*******", "title": "******* Notification Components", "Content": "Component  Type  Purpose  \nSuccess Messages  Alert  Confirmation of successful \nactions (green styling)  \nError Messages  Alert  Display of errors and \nvalidation failures (red \nstyling)  \nWarning Messages  Alert  Important notices and \ncautions (yellow/orange \nstyling)  \nInfo Messages  Alert  General information and \ntips (blue styling)  \nToast Notifications  Popup  Temporary notifications for \nquick feedback"}, {"heading_number": "*******", "title": "******* Data Display Components", "Content": "Component  Type  Usage  \nData Tables  Display  Structured display of \nproducts, orders, users with \nsorting and filtering  \nProduct Cards  Display  Consistent product \npresentation with image, \ntitle, price, actions  \nPagination  Navigation  Page navigation for large \ndata sets  \nLoading Spinners  Indicator  Visual feedback during data \nloading  \nStatus Badges  Indicator  Color -coded status \nindicators for orders, \nproducts, users  \nProgress Bars  Indicator  Progress indication for \nmulti -step processes"}, {"heading_number": "2.1.3", "title": "2.1.3 Detailed UI Elements by Screen", "Content": ""}, {"heading_number": "*******", "title": "******* Public Pages", "Content": ""}, {"heading_number": "*******.1", "title": "*******.1 Home Page Elements", "Content": "******* Public Pages  \n*******.1 Home  Page  Elements  \nUI Element  Type  Description  \nSite Header  Navigation  Logo, main navigation menu \n(SHOP, COLLABS, \nCONTACT, ABOUT US), user \naccount dropdown, \nshopping cart icon\n\nHero Banner  Visual  Promotional banner with \nrotating images and \nfeatured content  \nCategory Filter  Filter  Dropdown or buttons to \nfilter products by category  \nProduct Grid  Content  Grid layout displaying \nproduct cards with images, \nnames, prices, and quick \naction buttons  \nProduct Cards  Interactive  Individual product display \nwith image, title, price, and \n'Add to Cart' button  \nSearch Bar  Input  Global search functionality \nfor finding products  \nFooter  Navigation  Additional links, contact \ninformation, and site \npolicies"}, {"heading_number": "*******.2", "title": "*******.2 Product Detail Page Elements", "Content": "UI Element  Type  Description  \nProduct Images  Visual  Main product image with \nthumbnail gallery  \nProduct Information  Content  Product name, price, \ndescription, specifications, \nand seller information  \nQuantity Selector  Input  Numeric input for selecting \nproduct quantity  \nAdd to Cart Button  Action  Primary action button to \nadd product to shopping \ncart  \nStock Status  Indicator  Display of product \navailability and stock level  \nSeller Information  Content  Details about the product \nseller including sh"}, {"heading_number": "*******.3", "title": "*******.3 Search Results Page Elements", "Content": "UI Element  Type  Description  \nProduct Images  Visual  Main product image with \nthumbnail gallery  \nProduct Information  Content  Product name, price, \ndescription, specifications, \nand seller information  \nQuantity Selector  Input  Numeric input for selecting \nproduct quantity  \nAdd to Cart Button  Action  Primary action button to \nadd product to shopping \ncart  \nStock Status  Indicator  Display of product \navailability and stock level  \nSeller Information  Content  Details about the product \nseller including shop name \nand ratings  \n*******.3 Search Results Page  Elements  \nUI Element  Type  Description  \nSearch Query Display  Content  Shows the current search \nterm and number of results \nfound  \nFilter Options  Filter  Category filters, price range, \nand sorting options  \nResults Grid  Content  Grid layout of matching \nproducts with pagination  \nSort Controls  Control  Dropdown for sorting \nresults by price, name, \nrelevance, etc.\n\nNo Results Message  Content  Message displayed when no \nproducts match the search \ncriteri"}, {"heading_number": "*******.4", "title": "*******.4 About Us Page Elements", "Content": "UI Element  Type  Description  \nPage Title  Content  Main heading for the About \nUs section  \nCompany Information  Content  Text content describing the \ncompany mission, vision, \nand values  \nTeam Section  Content  Information about key team \nmembers or company \nhistory  \nContact Information  Content  Basic contact details and \nlinks to"}, {"heading_number": "*******.5", "title": "*******.5 Contact Page Elements", "Content": "UI Element  Type  Description  \nContact Form  Form  Input fields for name, email, \nsubject, and message  \nContact Information  Content  Company address, phone \nnumber, email, and \nbusiness hours  \nSubmit Button  Action  Button to send the contact \nform  \nSuccess/Error Messages  Feedback  Messages confirming form \nsubmission or displaying \nerrors  \nMap Integration  Visual  Embedded map showing \ncompany location"}, {"heading_number": "*******.6", "title": "*******.6 Collaborations Page Elements", "Content": "*******.6 Collaborations Page  Elements  \nUI Element  Type  Description  \nPage Title  Content  Main heading for \ncollaborations section  \nPartnership Information  Content  Details about collaboration \nopportunities and \npartnerships  \nPartner Showcase  Visual  Display of current partners \nor collaboration examples  \nContact for Partnerships  Content  Information on how to \ninitiate collaboration \ndiscussions"}, {"heading_number": "*******", "title": "******* Authentication Pages", "Content": ""}, {"heading_number": "*******.1", "title": "*******.1 Login Page Elements", "Content": "UI Element  Type  Description  \nLogin Form  Form  Email and password input \nfields with validation  \nLogin Button  Action  Primary button to submit \nlogin credentials  \nRemember Me Checkbox  Input  Option to keep user logged \nin across sessions  \nForgot Password Link  Navigation  Link to password recovery \nfunctionality  \nRegistration Links  Navigation  Links to customer and seller \nregistration pages  \nSocial Login Options  Action  Buttons for Facebook, \nGoogle, and Twitter \nauthentication  \nError Messages  Feedback  Display of login errors and"}, {"heading_number": "*******.2", "title": "*******.2 Customer Registration Page Elements", "Content": "UI Element  Type  Description  \nPersonal Information Form  Form  First name, last name, and \nemail input fields  \nPassword Fields  Input  Password and confirm \npassword fields with \nstrength indicator  \nTerms Agreement  Checkbox  Checkbox to agree to Terms \nof Service and Privacy \nPolicy  \nRegister Button  Action  Primary button to submit \nregistration form  \nPassword Strength \nIndicator  Visual  Real -time display of \npassword strength  \nForm Validation  Feedback  Real -time validation \nmessages for form fields  \nLogin Link  Navigation  Link to ex"}, {"heading_number": "2.*******", "title": "2.******* Seller Registration Page Elements", "Content": "UI Element  Type  Description  \nPersonal Information Form  Form  First name, last name, and \nemail input fields  \nPassword Fields  Input  Password and confirm \npassword fields with \nstrength indicator  \nTerms Agreement  Checkbox  Checkbox to agree to Terms \nof Service and Privacy \nPolicy  \nRegister Button  Action  Primary button to submit \nregistration form  \nPassword Strength \nIndicator  Visual  Real -time display of \npassword strength  \nForm Validation  Feedback  Real -time validation \nmessages for form fields  \nLogin Link  Navigation  Link to existing user login \npage  \n2.******* Seller Registration Page  Elements  \nUI Element  Type  Description  \nPersonal Information Form  Form  First name, last name, and \nemail input fields  \nShop Information  Form  Shop name and description \nfields  \nPassword Fields  Input  Password and confirm \npassword fields with \nstrength indicator\n\nTerms Agreement  Checkbox  Checkbox to agree to seller \nterms and conditions  \nRegister Button  Action  Primary button to submit \nseller registration  \nPassword Strength \nIndicator  Visual  Real -time display of \npassword strength  \nForm Validation  Feedback  Real -time validation \nmessages for all form fields  \nLogin Link  Navigation  Link to existing user login \npage"}, {"heading_number": "*******", "title": "******* Customer Pages", "Content": ""}, {"heading_number": "*******.1", "title": "*******.1 Shopping Cart Page Elements", "Content": "UI Element  Type  Description  \nCart Items List  Content  List of products added to \ncart with images, names, \nand prices  \nQuantity Controls  Input  Buttons and inputs to adjust \nitem quantities  \nRemove Item Buttons  Action  Buttons to remove \nindividual items from cart  \nSeller Grouping  Organization  Items grouped by seller for \nmulti -vendor support  \nSubtotal Display  Content  Running total for each seller \nand overall cart total  \nContinue Shopping Button  Navigation  Link to return to product \nbrowsing  \nCheckout Button  Action  Primary button to proceed \nto checkout process  \nEmpty Cart Message  Content  Message displayed when \ncart is empty"}, {"heading_number": "*******.2", "title": "*******.2 Customer Bookings Page Elements", "Content": "UI Element  Type  Description  \nBookings List  Content  List of customer's service \nbookings with status \nindicators  \nBooking Status  Indicator  Visual indicators for \npending, confirmed, \ncompleted, or cancelled \nbookings  \nBooking Details Link  Navigation  Links to view detailed \ninformation for each \nbooking  \nFilter Options  Filter  Options to filter bookings \nby status or date  \nCreate New Booking  Action  Button to initiate new \nservice booking\n\nSearch Functionality  Input  Search bar to find specific \nbookings"}, {"heading_number": "*******.3", "title": "*******.3 Booking Details Page Elements", "Content": "UI Element  Type  Description  \nBooking Information  Content  Complete booking details \nincluding service, date, \ntime, and location  \nStatus Display  Indicator  Current booking status with \nvisual indicators  \nService Provider Info  Content  Information about the \nservice provider/seller  \nCustomer Information  Content  Customer details and \ncontact information  \nSpecial Instructions  Content  Any special requests or \ninstructions for the service  \nAction Buttons  Action  Buttons for booking actions \nlike cancel or modify (if \napplicable)  \nBack to Bookings  Navigation  Link to re"}, {"heading_number": "*******.4", "title": "*******.4 Create Booking Page Elements", "Content": "UI Element  Type  Description  \nService Selection  Input  Dropdown or selection \ninterface for available \nservices  \nAddress Form  Form  Address input fields for \nservice location  \nDate/Time Picker  Input  Calendar and time selection \nfor booking appointment  \nSpecial Instructions  Input  Text area for additional \nrequests or notes  \nAddress Selection  Input  Option to use saved \naddresses or enter new \naddress  \nBooking Summary  Content  Summary of selected \nservice, date, time, and \nlocation  \nSubmit Booking Button  Action  Primary button to create \nthe booking"}, {"heading_number": "*******", "title": "******* Account Management Pages", "Content": ""}, {"heading_number": "*******.1", "title": "*******.1 User Profile Page Elements", "Content": "*******.1 User Profile Page  Elements  \nUI Element  Type  Description  \nProfile Picture  Visual  User avatar/profile image \nwith upload option\n\nPersonal Information  Content  Display of user's name, \nemail, and contact details  \nAccount Statistics  Content  Summary of account \nactivity, orders, and \nmembership dura"}, {"heading_number": "*******.2", "title": "*******.2 Edit Profile Page Elements", "Content": "editing page"}, {"heading_number": "*******.3", "title": "*******.3 Change Password Page Elements", "Content": "functionality  \nAccount Settings  Navigation  Links to various account \nmanagement options  \n*******.2 Edit Profile Page  Elements  \nUI Element  Type  Description  \nProfile Form  Form  Editable fields for first \nname, last name, and \ncontact information  \nProfile Image Upload  Input  File upload interface for \nchanging profile picture  \nSave Changes Button  Action  Primary button to save \nprofile updates  \nCancel Button  Action  Button to discard changes \nand return to profile view  \nForm Validation  Feedback  Real -time validation for \nform fields  \nSuccess Messages  Feedback  Confirmation messages for \nsuccessful updates  \nPreview Image  Visual  Preview of uploaded profile \nimage before saving  \n*******.3 Change Password Page  Elements  \nUI Element  Type  Description  \nCurrent Password Field  Input  Secure input for current \npassword verification  \nNew Password Field  Input  Secure input for new \npassword with strength \nindicator  \nConfirm Password Field  Input  Secure input to confirm new \npassword  \nPassword Strength \nIndicator  Visual  Real -time display of new \npassword strength  \nChange Password Button  Action  Primary button to update \npassword  \nForm Validation  Feedback  Validat"}, {"heading_number": "*******.4", "title": "*******.4 User Orders Page Elements", "Content": "matching  \n*******.4 User Orders Page  Elements  \nUI Element  Type  Description\n\nOrders List  Content  List of user's purchase \norders with basic \ninformation  \nOrder Status  Indicator  Visual indicators for order \nstatus (pending, confirmed, \nshipped, delivered)  \nOrder Details Link  Navigation  Links to view detailed \ninformation for each order  \nFilter Options  Filter  Options to filter orders by \nstatus, date, or seller  \nSearch Functionality  Input  Search bar to find specific \norders  \nPagination  Navigation  Page navigation for large \norder lists  \nOrder Summary  Content  Brief summary showing \ntotal orders and recent \nactivity"}, {"heading_number": "*******", "title": "******* <PERSON><PERSON> Pages", "Content": ""}, {"heading_number": "*******.1", "title": "*******.1 Seller Dashboard Elements", "Content": "UI Element  Type  Description  \nDashboard Overview  Content  Summary cards showing \nkey metrics (total products, \nsales, orders)  \nRecent Orders  Content  List of recent customer \norders requiring attention  \nProduct Statistics  Visual  Charts or graphs showing \nproduct performance  \nQuick Actions  Action  Buttons for common tasks \nlike adding products or \nviewing orders  \nSales Metrics  Content  Revenue and sales \nperformance indicators  \nInventory Alerts  Notification  Alerts for low stock or out -\nof-stock products  \nNavigation Menu  Navigation  Sidebar or menu for \naccessing seller functions  \nShop Information  Content  Display of shop"}, {"heading_number": "*******.2", "title": "*******.2 Seller Products Page Elements", "Content": "*******.2 Seller Products Page  Elements  \nUI Element  Type  Description  \nProducts List  Content  Table or grid view of seller's \nproducts with key \ninformation  \nAdd Product Button  Action  Primary button to create \nnew product listings\n\nProduct Actions  Action  Edit, delete, and status \nchange buttons for each \nproduct  \nSearch/Filter  Input  Tools to search and filter \nproducts by various criteria  \nProduct Status  Indicator  Visual indicators for \nproduct status (active, \npending, inactive)  \nStock Levels  Content  Display of current inventory \nlevels for each product  \nBulk Actions  Action  Options to perform actions \non multiple products \nsimultaneously  \nPagination  Navigation  Page navigation for large"}, {"heading_number": "*******.3", "title": "*******.3 Edit Product Page Elements", "Content": "UI Element  Type  Description  \nProduct Form  Form  Comprehensive form with \nfields for product details \n(name, description, price)  \nImage Upload  Input  Interface for uploading and \nmanaging product images  \nCategory Selection  Input  Dropdown for selecting \nproduct category  \nInventory Management  Input  Fields for stock quantity \nand inventory tracking  \nPricing Information  Input  Fields for price, discounts, \nand pricing rules  \nProduct Status  Input  Options to set product as \nactive, inactive, or draft  \nSave/Update Button  Action  Primary button to save \nproduct changes  \nCancel Button  Action  Button to discard changes \nand return to products list  \nForm Validation  Feedback  Real -time validation for \nrequi"}, {"heading_number": "*******.4", "title": "*******.4 Seller Orders Page Elements", "Content": "formats  \n*******.4 Seller Orders Page  Elements  \nUI Element  Type  Description  \nOrders List  Content  Table displaying orders for \nseller's products  \nOrder Status Filter  Filter  Dropdown to filter orders \nby status (pending, \nconfirmed, completed, \ncancelled)  \nOrder Details Link  Navigation  Links to view complete \norder information\n\nStatus Update Actions  Action  Buttons to change order \nstatus (confirm, ship, \ncomplete)  \nCustomer Information  Content  Basic customer details for \neach order  \nOrder Value  Content  Total value and item count \nfor each order  \nSearch Functionality  Input  Search bar to find specific \norders  \nExport Options  Action  Buttons to export order \ndata"}, {"heading_number": "*******.5", "title": "*******.5 Seller Order Details Page Elements", "Content": "UI Element  Type  Description  \nOrder Summary  Content  Complete order information \nincluding order number, \ndate, and status  \nCustomer Details  Content  Full customer information \nand contact details  \nShipping Address  Content  Delivery address and \nspecial instructions  \nOrder Items  Content  Detailed list of products, \nquantities, and prices  \nPayment Information  Content  Payment method and \ntransaction details  \nStatus Timeline  Visual  Timeline showing order \nstatus progression  \nAction Buttons  Action  Buttons for status updates \nand order management  \nBack to Orders  Navigation  Link t"}, {"heading_number": "*******.6", "title": "*******.6 Seller Bookings Page Elements", "Content": "*******.6 Seller Bookings Page  Elements  \nUI Element  Type  Description  \nBookings List  Content  Table displaying service \nbookings for the seller  \nBooking Status Filter  Filter  Options to filter bookings \nby status  \nBooking Details Link  Navigation  Links to view detailed \nbooking information  \nStatus Update Actions  Action  Buttons to confirm, \ncomplete, or cancel \nbookings  \nCustomer Information  Content  Customer details and \ncontact information  \nService Details  Content  Information about the \nbooked service  \nCalendar View  Visual  Optional calendar interface \nfor viewing bookings by \ndate"}, {"heading_number": "*******.7", "title": "*******.7 Seller Booking Details Page Elements", "Content": "UI Element  Type  Description  \nBooking Information  Content  Complete booking details \nincluding service, date, \ntime, and location  \nCustomer Details  Content  Full customer information \nand contact details  \nService Location  Content  Address and location details \nfor service delivery  \nSpecial Instructions  Content  Customer requests and \nspecial instructions  \nStatus Management  Action  Buttons to update booking \nstatus  \nCommunication Tools  Action  Options to contact customer \nregarding the booking  \nBack to Bookings  Navigation  Link to return to bookings \nlist"}, {"heading_number": "*******", "title": "******* Admin Pages", "Content": ""}, {"heading_number": "*******.1", "title": "*******.1 Admin Dashboard Elements", "Content": "UI Element  Type  Description  \nSystem Overview  Content  High -level metrics showing \ntotal users, products, \norders, and revenue  \nRecent Activity  Content  Feed of recent system \nactivities and user actions  \nUser Statistics  Visual  Charts showing user \nregistration trends and \nactivity  \nSales Analytics  Visual  Graphs and charts \ndisplaying sales \nperformance and trends  \nProduct Statistics  Content  Summary of total products, \ncategories, and pending \napprovals  \nOrder Management  Content  Overview of order statuses \nand recent transactions  \nQuick Actions  Action  Buttons for common admin \ntasks  \nSystem Alerts  Notification  Important system \nnotifications and alerts  \nNavigation Menu  Navigation  Admin pan"}, {"heading_number": "*******.2", "title": "*******.2 Admin Products Page Elements", "Content": "sidebar or menu  \n*******.2 Admin Products Page  Elements  \nUI Element  Type  Description  \nProducts List  Content  Comprehensive table of all \nproducts in the system\n\nSearch and Filter  Input  Advanced search and \nfiltering options by \ncategory, seller, status  \nProduct Actions  Action  Buttons to approve, reject, \nedit, or delete products  \nBulk Actions  Action  Options to perform actions \non multiple products  \nProduct Status  Indicator  Visual indicators for \nproduct approval status  \nSeller Information  Content  Details about the product \nseller  \nCategory Management  Navigation  Links to category \nmanagement functions  \nExport Options  Action  Tools to export product"}, {"heading_number": "*******.3", "title": "*******.3 Admin Pending Products Page Elements", "Content": "UI Element  Type  Description  \nPending Products List  Content  Table of products awaiting \nadmin approval  \nProduct Preview  Visual  Quick preview of product \ndetails and images  \nApproval Actions  Action  Buttons to approve or reject \npending products  \nRejection Reasons  Input  Options to provide reasons \nfor product rejection  \nSeller Information  Content  Details about the seller \nsubmitting the product  \nBulk Approval  Action  Options to approve multiple \nproducts simultaneously  \nReview Comments  Input  Text area for admin \ncomment"}, {"heading_number": "*******.4", "title": "*******.4 Admin Categories Page Elements", "Content": "*******.4 Admin Categories Page  Elements  \nUI Element  Type  Description  \nCategories List  Content  Table displaying all product \ncategories  \nAdd Category Form  Form  Form to create new product \ncategories  \nCategory Actions  Action  Buttons to edit, delete, or \nmodify categories  \nCategory Hierarchy  Visual  Display of parent -child \ncategory relationships  \nProduct Count  Content  Number of products in each \ncategory  \nCategory Status  Indicator  Active/inactive status for \neach category\n\nBulk Management  Action  Options for bulk category \noperations"}, {"heading_number": "*******.5", "title": "*******.5 Admin Users Page Elements", "Content": "UI Element  Type  Description  \nUsers List  Content  Comprehensive table of all \nsystem users  \nUser Role Filter  Filter  Options to filter users by \nrole (customer, seller, \nadmin)  \nUser Actions  Action  Buttons to activate, \ndeactivate, or modify user \naccounts  \nUser Details  Content  Display of user information \nincluding registration date \nand activity  \nSearch Functionality  Input  Search bar to find specific \nusers  \nAccount Status  Indicator  Visual indicators for user \naccount status  \nRole Management  Action  Options to change user \nroles and permissions  \nExport Options  Action  Tools to expor"}, {"heading_number": "*******.6", "title": "*******.6 Admin Orders Page Elements", "Content": "UI Element  Type  Description  \nOrders List  Content  Comprehensive table of all \nsystem orders  \nOrder Status Filter  Filter  Options to filter orders by \nstatus and s"}, {"heading_number": "*******.7", "title": "*******.7 Admin Order Details Page Elements", "Content": "Order Details Link  Navigation  Links to view detailed order \ninformation  \nStatus Management  Action  Admin controls for order \nstatus updates  \nCustomer and Seller Info  Content  Information about order \nparticipants  \nOrder Value  Content  Financial information for \neach order  \nSearch and Filter  Input  Advanced search options \nfor finding specific orders  \nAnalytics Tools  Action  Links to order analytics and \nreporting  \n*******.7 Admin Order Details Page  Elements  \nUI Element  Type  Description  \nOrder Summary  Content  Complete order information \nand transaction details\n\nCustomer Information  Content  Full customer profile and \ncontact details  \nSeller Information  Content  Details about the seller and \nshop  \nOrder Items  Content  Detailed breakdown of \nproducts, quantities, and \npricing  \nPayment Details  Content  Payment method, \ntransaction ID, and financial \ninformation  \nShipping Information  Content  Delivery address and \nshipping method  \nOrder Timeline  Visual  Timeline showing order \nstatus progression  \nAdmin Actions  Action  Administrative controls f"}, {"heading_number": "*******.8", "title": "*******.8 Admin Sale Codes Page Elements", "Content": "UI Element  Type  Description  \nSale Codes List  Content  Table of all promotional \ncodes and discounts  \nCreate Code Form  Form  Form to create new \npromotional codes  \nCode Management  Action  Buttons to edit, activate, \ndeactivate, or delete codes  \nUsage Statistics  Content  Information about code \nusage and effectiveness  \nCode Details  Content  Discount amount, \nexpiration date, and usage \nlimits  \nSearch and Filter  Input  Options to search and filter \npromotional codes  \nBulk Actions  Action  Options for bulk \nmanagement of \npromotional codes"}, {"heading_number": "*******", "title": "******* Error and System Pages", "Content": ""}, {"heading_number": "*******.1", "title": "*******.1 Access Denied Page Elements", "Content": "*******.1 Access Denied Page  Elements  \nUI Element  Type  Description  \nError Message  Content  Clear message explaining \naccess restriction  \nError Code  Content  HTTP 403 error code \ndisplay  \nNavigation Options  Navigation  Links to return to accessible \nareas  \nLogin Suggestion  Action  Option to login with \nappropriate credentials\n\nContact Support  Navigation  Link to contact support for \naccess issues"}, {"heading_number": "*******.2", "title": "*******.2 <PERSON><PERSON><PERSON> Page Elements", "Content": "UI Element  Type  Description  \nError Message  Content  User -friendly error message \nexplaining the issue  \nError Code  Content  HTTP error code (404, 500, \netc.)  \nSuggested Actions  Content  Recommendations for \nresolving the error  \nHome Page Link  Navigation  Link to return to the main \npage  \nSearch Functionality  Input  Search bar to help users \nfind what they were looking \nfor \nContact Support  Navigation  Link to contact technical \nsupport"}, {"heading_number": "3", "title": "3. <PERSON>ppendi<PERSON>", "Content": "Document Version: 1.0  \n Last Updated: January 28, 2025  \n Document Status: To be continued  \n \nBUSINESS FLOW : COMPLETE FLOW BETWEEN CUSTOMER AND SELLER  \nThe interaction between a Customer and a Seller on the SASUCare platform represents a \ndynamic and continuous lifecycle, beginning long before a single purchase is made. Initially, \na Seller establishes their digital presence by setting up a detailed shop pr ofile (UC -301) and \nmeticulously populating their catalog with products, including images, descriptions, and \naccurate inventory levels (UC -302). A Customer then begins their journey by registering an \naccount (UC -101) or logging in (UC -102), discovering the Seller's offerings, and adding a \ndesired item to their shopping cart (UC -203). This culminates in the core transaction where \nthe Customer completes the checkout process, providing payment and shipping details to \nfinalize their order (UC -207). Immediately, the Seller is notified of the new sale and navigates \ntheir dashboard to manage the order (UC -305), updating its status from 'Processing' to \n'Shipped' after dispatching the item and providing a tracking number, which the Customer \ncan monitor through their o wn order history page (UC -208). Beyond this standard product \nflow, a different Customer might engage with the Seller's service offerings, viewing their \navailability and booking a specific appointment (UC -211). Should that Customer's plans \nchange, they can then navigate back to their bookings to request a cancellation, which the \nSeller reviews and processes according to their stated policy (UC -212). Following these\n\nsuccessful customer interactions, the Seller’s work continues as they might update their \nbusiness information, refine product listings, or add entirely new items, which may require a \nbrief period of pending approval before becoming visible to the public, e nsuring platform \nquality standards are met (reflecting UC -405). This entire ecosystem demonstrates a \ncontinuous loop of preparation, transaction, fulfillment, and ongoing management from both \nthe Customer and Seller perspectives."}]